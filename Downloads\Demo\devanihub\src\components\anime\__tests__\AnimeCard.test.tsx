import { describe, it, expect, vi } from 'vitest'
import { screen, fireEvent } from '@testing-library/react'
import { renderWithProviders, createMockAnimeWithEpisodes, testIds } from '../../../test/utils'
import AnimeCard from '../AnimeCard'

describe('AnimeCard', () => {
  const mockAnime = createMockAnimeWithEpisodes(5)
  const mockOnClick = vi.fn()

  beforeEach(() => {
    mockOnClick.mockClear()
  })

  it('renders anime information correctly', () => {
    renderWithProviders(
      <AnimeCard anime={mockAnime} onClick={mockOnClick} />
    )

    expect(screen.getByText(mockAnime.title)).toBeInTheDocument()
    expect(screen.getByText(mockAnime.genre)).toBeInTheDocument()
    expect(screen.getByText(`${mockAnime.episodeCount} episodes`)).toBeInTheDocument()
  })

  it('displays total view count correctly', () => {
    const animeWithViews = {
      ...mockAnime,
      episodes: [
        { ...mockAnime.episodes[0], viewCount: 100 },
        { ...mockAnime.episodes[1], viewCount: 200 },
        { ...mockAnime.episodes[2], viewCount: 300 },
      ]
    }

    renderWithProviders(
      <AnimeCard anime={animeWithViews} onClick={mockOnClick} />
    )

    expect(screen.getByText('600 views')).toBeInTheDocument()
  })

  it('calls onClick when card is clicked', () => {
    renderWithProviders(
      <AnimeCard anime={mockAnime} onClick={mockOnClick} />
    )

    const card = screen.getByRole('button')
    fireEvent.click(card)

    expect(mockOnClick).toHaveBeenCalledWith(mockAnime)
  })

  it('renders in grid view mode by default', () => {
    renderWithProviders(
      <AnimeCard anime={mockAnime} onClick={mockOnClick} />
    )

    const card = screen.getByRole('button')
    expect(card).toHaveClass('aspect-[3/4]') // Grid view aspect ratio
  })

  it('renders in list view mode when specified', () => {
    renderWithProviders(
      <AnimeCard anime={mockAnime} onClick={mockOnClick} viewMode="list" />
    )

    const card = screen.getByRole('button')
    expect(card).toHaveClass('flex-row') // List view layout
  })

  it('shows fallback when poster image fails to load', () => {
    const animeWithoutPoster = {
      ...mockAnime,
      posterUrl: ''
    }

    renderWithProviders(
      <AnimeCard anime={animeWithoutPoster} onClick={mockOnClick} />
    )

    // LazyImage should show fallback content
    expect(screen.getByRole('button')).toBeInTheDocument()
  })

  it('applies custom className when provided', () => {
    const customClass = 'custom-anime-card'
    
    renderWithProviders(
      <AnimeCard 
        anime={mockAnime} 
        onClick={mockOnClick} 
        className={customClass}
      />
    )

    const card = screen.getByRole('button')
    expect(card).toHaveClass(customClass)
  })

  it('handles anime with no episodes', () => {
    const animeWithoutEpisodes = {
      ...mockAnime,
      episodes: [],
      episodeCount: 0
    }

    renderWithProviders(
      <AnimeCard anime={animeWithoutEpisodes} onClick={mockOnClick} />
    )

    expect(screen.getByText('0 episodes')).toBeInTheDocument()
    expect(screen.getByText('0 views')).toBeInTheDocument()
  })

  it('truncates long titles appropriately', () => {
    const animeWithLongTitle = {
      ...mockAnime,
      title: 'This is a very long anime title that should be truncated when displayed in the card component'
    }

    renderWithProviders(
      <AnimeCard anime={animeWithLongTitle} onClick={mockOnClick} />
    )

    const titleElement = screen.getByText(animeWithLongTitle.title)
    expect(titleElement).toHaveClass('line-clamp-2')
  })

  it('shows correct status badge', () => {
    const ongoingAnime = { ...mockAnime, status: 'ongoing' as const }
    const completedAnime = { ...mockAnime, status: 'completed' as const }

    const { rerender } = renderWithProviders(
      <AnimeCard anime={ongoingAnime} onClick={mockOnClick} />
    )

    expect(screen.getByText('ongoing')).toBeInTheDocument()

    rerender(<AnimeCard anime={completedAnime} onClick={mockOnClick} />)
    expect(screen.getByText('completed')).toBeInTheDocument()
  })

  it('has proper accessibility attributes', () => {
    renderWithProviders(
      <AnimeCard anime={mockAnime} onClick={mockOnClick} />
    )

    const card = screen.getByRole('button')
    expect(card).toHaveAttribute('tabIndex', '0')
    
    // Should have proper ARIA label or accessible name
    expect(card).toHaveAccessibleName()
  })

  it('supports keyboard navigation', () => {
    renderWithProviders(
      <AnimeCard anime={mockAnime} onClick={mockOnClick} />
    )

    const card = screen.getByRole('button')
    card.focus()
    
    fireEvent.keyDown(card, { key: 'Enter' })
    expect(mockOnClick).toHaveBeenCalledWith(mockAnime)

    mockOnClick.mockClear()
    
    fireEvent.keyDown(card, { key: ' ' })
    expect(mockOnClick).toHaveBeenCalledWith(mockAnime)
  })

  it('applies touch-friendly styles on mobile', () => {
    renderWithProviders(
      <AnimeCard anime={mockAnime} onClick={mockOnClick} />
    )

    const card = screen.getByRole('button')
    expect(card).toHaveClass('touch-manipulation')
  })
})
