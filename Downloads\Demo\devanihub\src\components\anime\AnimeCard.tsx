import React from 'react'
import { <PERSON>, Eye } from 'lucide-react'
import { Card } from '../ui/Card'
import { LazyImage } from '../common/LazyImage'
import { cn } from '../../lib/utils'
import type { Anime } from '../../types'

interface AnimeCardProps {
  anime: Anime
  onClick: () => void
  className?: string
  viewMode?: 'grid' | 'list'
}

const AnimeCard: React.FC<AnimeCardProps> = ({ anime, onClick, className, viewMode = 'grid' }) => {
  const [imageLoaded, setImageLoaded] = React.useState(false)
  const [imageError, setImageError] = React.useState(false)
  
  const totalViews = anime.episodes.reduce((sum, ep) => sum + ep.viewCount, 0)
  
  const handleImageLoad = () => {
    setImageLoaded(true)
  }
  
  const handleImageError = () => {
    setImageError(true)
    setImageLoaded(true)
  }

  return (
    <Card
      className={cn(
        "group cursor-pointer overflow-hidden transition-all duration-300 hover:shadow-lg animate-fade-in",
        "active:scale-[0.98] touch-manipulation", // Touch feedback
        "hover:scale-105", // Desktop hover effect
        className
      )}
      onClick={onClick}
    >
      {/* Poster Container */}
      <div className="relative aspect-[3/4] overflow-hidden bg-muted">
        {!imageLoaded && (
          <div className="absolute inset-0 bg-muted animate-pulse" />
        )}
        
        <LazyImage
          src={anime.posterUrl}
          alt={anime.title}
          className="w-full h-full object-cover transition-all duration-300 group-hover:scale-110"
          containerClassName="absolute inset-0"
          fallback={
            <div className="flex items-center justify-center h-full bg-muted">
              <Play className="h-12 w-12 text-muted-foreground" />
            </div>
          }
        />
        
        {/* Episode Count Badge */}
        <div className="absolute top-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded-full backdrop-blur-sm">
          {anime.episodeCount} eps
        </div>
        
        {/* Season Badge */}
        <div className="absolute top-2 left-2 bg-primary/90 text-primary-foreground text-xs px-2 py-1 rounded-full backdrop-blur-sm">
          S{anime.season}
        </div>
        
        {/* Hover Overlay (desktop only) */}
        <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 items-center justify-center hidden sm:flex">
          <Play className="h-8 w-8 sm:h-12 sm:w-12 text-white" />
        </div>
      </div>
      
      {/* Content */}
      <div className="p-3 sm:p-4">
        <h3 className="font-semibold text-sm sm:text-base line-clamp-2 mb-2 group-hover:text-primary transition-colors">
          {anime.title}
        </h3>
        
        {/* Tags */}
        <div className="flex flex-wrap gap-1 mb-2">
          {anime.tags.slice(0, 2).map((tag) => (
            <span
              key={tag}
              className="text-xs bg-secondary text-secondary-foreground px-2 py-1 rounded-full"
            >
              {tag}
            </span>
          ))}
          {anime.tags.length > 2 && (
            <span className="text-xs text-muted-foreground">
              +{anime.tags.length - 2}
            </span>
          )}
        </div>
        
        {/* Stats */}
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <span className="flex items-center gap-1">
            <Eye className="h-3 w-3" />
            <span className="hidden sm:inline">{totalViews.toLocaleString()}</span>
            <span className="sm:hidden">{totalViews > 1000 ? `${(totalViews/1000).toFixed(1)}k` : totalViews}</span>
          </span>
          <span className="truncate ml-2">{anime.genre}</span>
        </div>
      </div>
    </Card>
  )
}

export default AnimeCard
