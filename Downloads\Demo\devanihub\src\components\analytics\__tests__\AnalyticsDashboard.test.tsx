import { describe, it, expect, vi } from 'vitest'
import { screen, waitFor } from '@testing-library/react'
import { renderWithProviders, createMockAnimeWithEpisodes, mockApiResponse } from '../../../test/utils'
import { AnalyticsDashboard } from '../AnalyticsDashboard'
import * as api from '../../../store/api'

// Mock the API hook
vi.mock('../../../store/api', () => ({
  useGetAnimesQuery: vi.fn(),
}))

describe('AnalyticsDashboard', () => {
  const mockAnimes = [
    createMockAnimeWithEpisodes(3),
    {
      ...createMockAnimeWithEpisodes(5),
      id: 'anime-2',
      title: 'Second Anime',
      genre: 'Comedy',
      episodes: [
        { id: 'ep-1', episodeNumber: 1, viewCount: 500, animeId: 'anime-2', title: 'Episode 1', description: '', duration: 1440, streamingLinks: [], thumbnailUrl: '', createdAt: '', updatedAt: '' },
        { id: 'ep-2', episodeNumber: 2, viewCount: 300, animeId: 'anime-2', title: 'Episode 2', description: '', duration: 1440, streamingLinks: [], thumbnailUrl: '', createdAt: '', updatedAt: '' },
      ]
    }
  ]

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders loading state correctly', () => {
    vi.mocked(api.useGetAnimesQuery).mockReturnValue(
      mockApiResponse(null, true, null)
    )

    renderWithProviders(<AnalyticsDashboard />)

    expect(screen.getByRole('status')).toBeInTheDocument() // Loading spinner
  })

  it('renders error state correctly', () => {
    vi.mocked(api.useGetAnimesQuery).mockReturnValue(
      mockApiResponse(null, false, { message: 'Failed to fetch' })
    )

    renderWithProviders(<AnalyticsDashboard />)

    expect(screen.getByText('Failed to load analytics data')).toBeInTheDocument()
  })

  it('displays analytics metrics correctly', async () => {
    vi.mocked(api.useGetAnimesQuery).mockReturnValue(
      mockApiResponse({ data: mockAnimes }, false, null)
    )

    renderWithProviders(<AnalyticsDashboard />)

    await waitFor(() => {
      expect(screen.getByText('Analytics Dashboard')).toBeInTheDocument()
    })

    // Check total animes
    expect(screen.getByText('2')).toBeInTheDocument() // Total animes count

    // Check total episodes
    expect(screen.getByText('5')).toBeInTheDocument() // Total episodes (3 + 2)

    // Check total views
    const totalViews = 100 * 3 + 500 + 300 // Default 100 per episode + custom views
    expect(screen.getByText(totalViews.toLocaleString())).toBeInTheDocument()

    // Check average rating
    expect(screen.getByText('4.2')).toBeInTheDocument()
  })

  it('shows most popular anime correctly', async () => {
    vi.mocked(api.useGetAnimesQuery).mockReturnValue(
      mockApiResponse({ data: mockAnimes }, false, null)
    )

    renderWithProviders(<AnalyticsDashboard />)

    await waitFor(() => {
      expect(screen.getByText('Most Popular Anime')).toBeInTheDocument()
    })

    // Should show the anime with highest total views
    expect(screen.getByText('Second Anime')).toBeInTheDocument()
    expect(screen.getByText('Comedy')).toBeInTheDocument()
    expect(screen.getByText('800 views')).toBeInTheDocument() // 500 + 300
  })

  it('displays top genres correctly', async () => {
    vi.mocked(api.useGetAnimesQuery).mockReturnValue(
      mockApiResponse({ data: mockAnimes }, false, null)
    )

    renderWithProviders(<AnalyticsDashboard />)

    await waitFor(() => {
      expect(screen.getByText('Top Genres')).toBeInTheDocument()
    })

    // Should show genres with counts
    expect(screen.getByText('Action')).toBeInTheDocument()
    expect(screen.getByText('Comedy')).toBeInTheDocument()
  })

  it('shows recently added section when there are recent animes', async () => {
    const recentAnime = {
      ...createMockAnimeWithEpisodes(1),
      id: 'recent-anime',
      title: 'Recent Anime',
      createdAt: new Date().toISOString() // Today
    }

    vi.mocked(api.useGetAnimesQuery).mockReturnValue(
      mockApiResponse({ data: [recentAnime] }, false, null)
    )

    renderWithProviders(<AnalyticsDashboard />)

    await waitFor(() => {
      expect(screen.getByText('Recently Added')).toBeInTheDocument()
    })

    expect(screen.getByText('Recent Anime')).toBeInTheDocument()
  })

  it('handles empty data gracefully', async () => {
    vi.mocked(api.useGetAnimesQuery).mockReturnValue(
      mockApiResponse({ data: [] }, false, null)
    )

    renderWithProviders(<AnalyticsDashboard />)

    await waitFor(() => {
      expect(screen.getByText('Analytics Dashboard')).toBeInTheDocument()
    })

    // Should show zero values
    expect(screen.getByText('0')).toBeInTheDocument() // Total animes
    expect(screen.getByText('No data available')).toBeInTheDocument() // Most popular
  })

  it('displays last updated timestamp', async () => {
    vi.mocked(api.useGetAnimesQuery).mockReturnValue(
      mockApiResponse({ data: mockAnimes }, false, null)
    )

    renderWithProviders(<AnalyticsDashboard />)

    await waitFor(() => {
      const today = new Date().toLocaleDateString()
      expect(screen.getByText(`Last updated: ${today}`)).toBeInTheDocument()
    })
  })

  it('shows proper icons for each metric', async () => {
    vi.mocked(api.useGetAnimesQuery).mockReturnValue(
      mockApiResponse({ data: mockAnimes }, false, null)
    )

    renderWithProviders(<AnalyticsDashboard />)

    await waitFor(() => {
      expect(screen.getByText('Total Animes')).toBeInTheDocument()
      expect(screen.getByText('Episodes')).toBeInTheDocument()
      expect(screen.getByText('Total Views')).toBeInTheDocument()
      expect(screen.getByText('Avg Rating')).toBeInTheDocument()
    })
  })

  it('calculates views this month correctly', async () => {
    vi.mocked(api.useGetAnimesQuery).mockReturnValue(
      mockApiResponse({ data: mockAnimes }, false, null)
    )

    renderWithProviders(<AnalyticsDashboard />)

    await waitFor(() => {
      expect(screen.getByText('Analytics Dashboard')).toBeInTheDocument()
    })

    // Views this month should be 30% of total views (mocked calculation)
    const totalViews = 100 * 3 + 500 + 300
    const viewsThisMonth = Math.floor(totalViews * 0.3)
    
    // The component should calculate and display this value
    expect(screen.getByText(totalViews.toLocaleString())).toBeInTheDocument()
  })

  it('handles anime with missing poster images', async () => {
    const animeWithoutPoster = {
      ...mockAnimes[0],
      posterUrl: ''
    }

    vi.mocked(api.useGetAnimesQuery).mockReturnValue(
      mockApiResponse({ data: [animeWithoutPoster] }, false, null)
    )

    renderWithProviders(<AnalyticsDashboard />)

    await waitFor(() => {
      expect(screen.getByText('Analytics Dashboard')).toBeInTheDocument()
    })

    // Should still render without errors
    expect(screen.getByText(animeWithoutPoster.title)).toBeInTheDocument()
  })
})
