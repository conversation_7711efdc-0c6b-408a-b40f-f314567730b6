# DevAniHub Deployment Guide

## Overview
DevAniHub is a full-stack React application built with TypeScript, Vite, and Firebase. This guide covers deployment options and configuration.

## Prerequisites
- Node.js 18+ installed
- Firebase CLI installed (`npm install -g firebase-tools`)
- Git repository set up

## Environment Setup

### 1. Environment Variables
Create a `.env.production` file in the root directory:

```env
VITE_FIREBASE_API_KEY=your_firebase_api_key
VITE_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
VITE_FIREBASE_APP_ID=your_app_id
VITE_APP_ENV=production
```

### 2. Firebase Configuration
Initialize Firebase in your project:

```bash
firebase login
firebase init
```

Select:
- Hosting
- Firestore
- Storage
- Functions (optional)

## Deployment Options

### Option 1: Firebase Hosting (Recommended)

#### Setup
1. Configure `firebase.json`:
```json
{
  "hosting": {
    "public": "dist",
    "ignore": [
      "firebase.json",
      "**/.*",
      "**/node_modules/**"
    ],
    "rewrites": [
      {
        "source": "**",
        "destination": "/index.html"
      }
    ],
    "headers": [
      {
        "source": "/service-worker.js",
        "headers": [
          {
            "key": "Cache-Control",
            "value": "no-cache"
          }
        ]
      },
      {
        "source": "**/*.@(js|css)",
        "headers": [
          {
            "key": "Cache-Control",
            "value": "max-age=31536000"
          }
        ]
      }
    ]
  },
  "firestore": {
    "rules": "firestore.rules",
    "indexes": "firestore.indexes.json"
  },
  "storage": {
    "rules": "storage.rules"
  }
}
```

#### Deploy
```bash
npm run build
firebase deploy
```

### Option 2: Vercel

#### Setup
1. Install Vercel CLI: `npm install -g vercel`
2. Create `vercel.json`:
```json
{
  "buildCommand": "npm run build",
  "outputDirectory": "dist",
  "devCommand": "npm run dev",
  "installCommand": "npm install",
  "framework": "vite",
  "rewrites": [
    {
      "source": "/(.*)",
      "destination": "/index.html"
    }
  ]
}
```

#### Deploy
```bash
vercel --prod
```

### Option 3: Netlify

#### Setup
1. Create `netlify.toml`:
```toml
[build]
  publish = "dist"
  command = "npm run build"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[[headers]]
  for = "/service-worker.js"
  [headers.values]
    Cache-Control = "no-cache"

[[headers]]
  for = "*.js"
  [headers.values]
    Cache-Control = "max-age=31536000"

[[headers]]
  for = "*.css"
  [headers.values]
    Cache-Control = "max-age=31536000"
```

#### Deploy
```bash
npm run build
# Upload dist folder to Netlify or connect Git repository
```

## Build Optimization

### 1. Vite Configuration
Update `vite.config.ts` for production:

```typescript
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          ui: ['@radix-ui/react-dialog', '@radix-ui/react-dropdown-menu'],
          firebase: ['firebase/app', 'firebase/firestore', 'firebase/storage']
        }
      }
    },
    chunkSizeWarningLimit: 1000
  },
  define: {
    'process.env.NODE_ENV': JSON.stringify('production')
  }
})
```

### 2. Performance Optimizations
- Enable gzip compression
- Configure CDN for static assets
- Implement service worker caching
- Use lazy loading for routes

## Security Configuration

### 1. Firestore Rules
Create `firestore.rules`:
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /animes/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    match /episodes/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }
  }
}
```

### 2. Storage Rules
Create `storage.rules`:
```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /posters/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null && 
        request.resource.size < 5 * 1024 * 1024 &&
        request.resource.contentType.matches('image/.*');
    }
  }
}
```

## Monitoring and Analytics

### 1. Firebase Analytics
Add to `main.tsx`:
```typescript
import { getAnalytics } from 'firebase/analytics'

if (import.meta.env.PROD) {
  getAnalytics(app)
}
```

### 2. Error Monitoring
Consider integrating:
- Sentry for error tracking
- LogRocket for session replay
- Google Analytics for user behavior

## CI/CD Pipeline

### GitHub Actions Example
Create `.github/workflows/deploy.yml`:
```yaml
name: Deploy to Firebase

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    
    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Build
      run: npm run build
      env:
        VITE_FIREBASE_API_KEY: ${{ secrets.FIREBASE_API_KEY }}
        VITE_FIREBASE_AUTH_DOMAIN: ${{ secrets.FIREBASE_AUTH_DOMAIN }}
        VITE_FIREBASE_PROJECT_ID: ${{ secrets.FIREBASE_PROJECT_ID }}
        VITE_FIREBASE_STORAGE_BUCKET: ${{ secrets.FIREBASE_STORAGE_BUCKET }}
        VITE_FIREBASE_MESSAGING_SENDER_ID: ${{ secrets.FIREBASE_MESSAGING_SENDER_ID }}
        VITE_FIREBASE_APP_ID: ${{ secrets.FIREBASE_APP_ID }}
    
    - name: Deploy to Firebase
      uses: FirebaseExtended/action-hosting-deploy@v0
      with:
        repoToken: '${{ secrets.GITHUB_TOKEN }}'
        firebaseServiceAccount: '${{ secrets.FIREBASE_SERVICE_ACCOUNT }}'
        projectId: your-project-id
```

## Post-Deployment Checklist

- [ ] Verify all routes work correctly
- [ ] Test mobile responsiveness
- [ ] Check service worker functionality
- [ ] Validate Firebase integration
- [ ] Test offline capabilities
- [ ] Monitor performance metrics
- [ ] Set up error tracking
- [ ] Configure domain (if custom)
- [ ] Enable HTTPS
- [ ] Test analytics integration

## Troubleshooting

### Common Issues
1. **Build fails**: Check environment variables and dependencies
2. **Routes not working**: Ensure proper rewrites configuration
3. **Firebase connection issues**: Verify API keys and project settings
4. **Service worker not updating**: Clear cache and check cache headers
5. **Performance issues**: Analyze bundle size and implement code splitting

### Support
- Check Firebase console for errors
- Review browser developer tools
- Monitor deployment logs
- Test in different browsers and devices
