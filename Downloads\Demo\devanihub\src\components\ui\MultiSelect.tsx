import * as React from "react"
import { X, Plus } from "lucide-react"
import { cn } from "../../lib/utils"
import { Badge } from "./Badge"
import { Input } from "./Input"
import { Button } from "./Button"

interface MultiSelectProps {
  options: string[]
  value: string[]
  onChange: (value: string[]) => void
  placeholder?: string
  className?: string
  allowCustom?: boolean
}

const MultiSelect: React.FC<MultiSelectProps> = ({
  options,
  value,
  onChange,
  placeholder = "Select tags...",
  className,
  allowCustom = true,
}) => {
  const [isOpen, setIsOpen] = React.useState(false)
  const [inputValue, setInputValue] = React.useState("")
  const [filteredOptions, setFilteredOptions] = React.useState(options)

  React.useEffect(() => {
    const filtered = options.filter(option =>
      option.toLowerCase().includes(inputValue.toLowerCase()) &&
      !value.includes(option)
    )
    setFilteredOptions(filtered)
  }, [inputValue, options, value])

  const handleSelect = (option: string) => {
    if (!value.includes(option)) {
      onChange([...value, option])
    }
    setInputValue("")
    setIsOpen(false)
  }

  const handleRemove = (option: string) => {
    onChange(value.filter(item => item !== option))
  }

  const handleAddCustom = () => {
    if (inputValue.trim() && !value.includes(inputValue.trim())) {
      onChange([...value, inputValue.trim()])
      setInputValue("")
      setIsOpen(false)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && allowCustom && inputValue.trim()) {
      e.preventDefault()
      handleAddCustom()
    } else if (e.key === 'Escape') {
      setIsOpen(false)
    }
  }

  return (
    <div className={cn("relative", className)}>
      {/* Selected Tags */}
      {value.length > 0 && (
        <div className="flex flex-wrap gap-1 mb-2">
          {value.map((tag) => (
            <Badge key={tag} variant="secondary" className="text-xs">
              {tag}
              <button
                type="button"
                onClick={() => handleRemove(tag)}
                className="ml-1 hover:text-destructive"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          ))}
        </div>
      )}

      {/* Input */}
      <div className="relative">
        <Input
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onFocus={() => setIsOpen(true)}
          onKeyDown={handleKeyDown}
          placeholder={value.length === 0 ? placeholder : "Add more tags..."}
          className="pr-10"
        />
        {allowCustom && inputValue.trim() && (
          <Button
            type="button"
            size="sm"
            variant="ghost"
            className="absolute right-1 top-1/2 -translate-y-1/2 h-7 w-7 p-0"
            onClick={handleAddCustom}
          >
            <Plus className="h-4 w-4" />
          </Button>
        )}
      </div>

      {/* Dropdown */}
      {isOpen && (filteredOptions.length > 0 || (allowCustom && inputValue.trim())) && (
        <div className="absolute top-full z-50 mt-1 w-full rounded-md border bg-popover shadow-md">
          <div className="max-h-60 overflow-auto p-1">
            {filteredOptions.map((option) => (
              <button
                key={option}
                type="button"
                className="relative flex w-full cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-accent hover:text-accent-foreground"
                onClick={() => handleSelect(option)}
              >
                {option}
              </button>
            ))}
            {allowCustom && inputValue.trim() && !options.includes(inputValue.trim()) && (
              <button
                type="button"
                className="relative flex w-full cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-accent hover:text-accent-foreground border-t"
                onClick={handleAddCustom}
              >
                <Plus className="mr-2 h-4 w-4" />
                Add "{inputValue.trim()}"
              </button>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

export { MultiSelect }
