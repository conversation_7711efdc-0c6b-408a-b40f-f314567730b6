import React from 'react'
import { render, type RenderOptions } from '@testing-library/react'
import { Provider } from 'react-redux'
import { BrowserRouter } from 'react-router-dom'
import { configureStore } from '@reduxjs/toolkit'
import { vi } from 'vitest'
import { animeApi } from '../store/api'
import type { Anime, Episode } from '../types'

// Mock store setup
const createMockStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      api: animeApi.reducer,
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware().concat(animeApi.middleware),
    preloadedState: initialState,
  })
}

// Custom render function with providers
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  initialState?: any
  store?: ReturnType<typeof createMockStore>
}

export function renderWithProviders(
  ui: React.ReactElement,
  {
    initialState = {},
    store = createMockStore(initialState),
    ...renderOptions
  }: CustomRenderOptions = {}
) {
  function Wrapper({ children }: { children: React.ReactNode }) {
    return (
      <Provider store={store}>
        <BrowserRouter>
          {children}
        </BrowserRouter>
      </Provider>
    )
  }

  return {
    store,
    ...render(ui, { wrapper: Wrapper, ...renderOptions })
  }
}

// Mock data generators
export const createMockAnime = (overrides: Partial<Anime> = {}): Anime => ({
  id: 'test-anime-1',
  title: 'Test Anime',
  description: 'A test anime description',
  genre: 'Action',
  releaseYear: 2023,
  status: 'ongoing',
  episodeCount: 12,
  posterUrl: 'https://example.com/poster.jpg',
  tags: ['action', 'adventure'],
  episodes: [],
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  ...overrides,
})

export const createMockEpisode = (overrides: Partial<Episode> = {}): Episode => ({
  id: 'test-episode-1',
  animeId: 'test-anime-1',
  episodeNumber: 1,
  title: 'Test Episode',
  description: 'A test episode description',
  duration: 1440, // 24 minutes in seconds
  streamingLinks: [
    {
      quality: '1080p',
      url: 'https://example.com/stream/1080p',
      provider: 'Test Provider'
    }
  ],
  thumbnailUrl: 'https://example.com/thumbnail.jpg',
  viewCount: 100,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  ...overrides,
})

export const createMockAnimeWithEpisodes = (episodeCount: number = 3): Anime => {
  const anime = createMockAnime()
  const episodes = Array.from({ length: episodeCount }, (_, index) =>
    createMockEpisode({
      id: `episode-${index + 1}`,
      episodeNumber: index + 1,
      title: `Episode ${index + 1}`,
    })
  )
  
  return {
    ...anime,
    episodes,
    episodeCount: episodes.length,
  }
}

// Mock API responses
export const mockApiResponse = <T,>(data: T, isLoading = false, error = null) => ({
  data,
  isLoading,
  error,
  refetch: vi.fn(),
})

// Mock file for testing file uploads
export const createMockFile = (
  name: string = 'test.jpg',
  type: string = 'image/jpeg',
  size: number = 1024
): File => {
  const file = new File(['test content'], name, { type })
  Object.defineProperty(file, 'size', { value: size })
  return file
}

// Mock drag and drop events
export const createMockDragEvent = (files: File[] = []) => {
  const event = new Event('drop') as any
  event.dataTransfer = {
    files,
    items: files.map(file => ({
      kind: 'file',
      type: file.type,
      getAsFile: () => file,
    })),
    types: ['Files'],
  }
  return event
}

// Wait for async operations
export const waitFor = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

// Mock intersection observer entries
export const createMockIntersectionObserverEntry = (
  isIntersecting: boolean = true
): IntersectionObserverEntry => ({
  isIntersecting,
  intersectionRatio: isIntersecting ? 1 : 0,
  target: document.createElement('div'),
  boundingClientRect: {} as DOMRectReadOnly,
  intersectionRect: {} as DOMRectReadOnly,
  rootBounds: {} as DOMRectReadOnly,
  time: Date.now(),
})

// Mock performance API
export const mockPerformance = {
  now: () => Date.now(),
  mark: vi.fn(),
  measure: vi.fn(),
  getEntriesByType: vi.fn(() => []),
  getEntriesByName: vi.fn(() => []),
}

// Test IDs for easier element selection
export const testIds = {
  animeCard: 'anime-card',
  animeTitle: 'anime-title',
  animeGenre: 'anime-genre',
  animeViewCount: 'anime-view-count',
  episodeCard: 'episode-card',
  episodeTitle: 'episode-title',
  searchInput: 'search-input',
  filterButton: 'filter-button',
  addAnimeButton: 'add-anime-button',
  mobileNavigation: 'mobile-navigation',
  analyticsCard: 'analytics-card',
} as const

export type TestIds = typeof testIds

// Custom matchers
declare global {
  namespace Vi {
    interface JestAssertion<T = any> {
      toBeInTheDocument(): T
      toHaveClass(className: string): T
      toHaveAttribute(attr: string, value?: string): T
    }
  }
}
