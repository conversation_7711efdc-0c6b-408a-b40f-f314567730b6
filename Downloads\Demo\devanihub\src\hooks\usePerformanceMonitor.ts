import { useEffect, useRef, useState } from 'react'

interface PerformanceMetrics {
  renderTime: number
  memoryUsage?: number
  fps: number
  loadTime: number
}

interface UsePerformanceMonitorOptions {
  trackFPS?: boolean
  trackMemory?: boolean
  sampleInterval?: number
}

export const usePerformanceMonitor = (
  componentName: string,
  options: UsePerformanceMonitorOptions = {}
) => {
  const {
    trackFPS = false,
    trackMemory = false,
    sampleInterval = 1000
  } = options

  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    renderTime: 0,
    fps: 0,
    loadTime: 0
  })

  const renderStartTime = useRef<number>(0)
  const frameCount = useRef<number>(0)
  const lastTime = useRef<number>(0)
  const animationFrameId = useRef<number>(0)

  // Track component render time
  useEffect(() => {
    renderStartTime.current = performance.now()
    
    return () => {
      const renderTime = performance.now() - renderStartTime.current
      setMetrics(prev => ({ ...prev, renderTime }))
      
      // Log performance data in development
      if (process.env.NODE_ENV === 'development') {
        console.log(`[Performance] ${componentName} render time: ${renderTime.toFixed(2)}ms`)
      }
    }
  })

  // Track FPS
  useEffect(() => {
    if (!trackFPS) return

    const measureFPS = (currentTime: number) => {
      frameCount.current++
      
      if (currentTime - lastTime.current >= sampleInterval) {
        const fps = Math.round((frameCount.current * 1000) / (currentTime - lastTime.current))
        setMetrics(prev => ({ ...prev, fps }))
        
        frameCount.current = 0
        lastTime.current = currentTime
      }
      
      animationFrameId.current = requestAnimationFrame(measureFPS)
    }

    animationFrameId.current = requestAnimationFrame(measureFPS)

    return () => {
      if (animationFrameId.current) {
        cancelAnimationFrame(animationFrameId.current)
      }
    }
  }, [trackFPS, sampleInterval])

  // Track memory usage
  useEffect(() => {
    if (!trackMemory || !('memory' in performance)) return

    const measureMemory = () => {
      const memory = (performance as any).memory
      if (memory) {
        const memoryUsage = Math.round(memory.usedJSHeapSize / 1024 / 1024) // MB
        setMetrics(prev => ({ ...prev, memoryUsage }))
      }
    }

    const interval = setInterval(measureMemory, sampleInterval)
    measureMemory() // Initial measurement

    return () => clearInterval(interval)
  }, [trackMemory, sampleInterval])

  // Track page load time
  useEffect(() => {
    const loadTime = performance.timing 
      ? performance.timing.loadEventEnd - performance.timing.navigationStart
      : 0
    
    setMetrics(prev => ({ ...prev, loadTime }))
  }, [])

  return metrics
}

// Hook for tracking specific operations
export const useOperationTimer = () => {
  const timers = useRef<Map<string, number>>(new Map())

  const startTimer = (operationName: string) => {
    timers.current.set(operationName, performance.now())
  }

  const endTimer = (operationName: string): number => {
    const startTime = timers.current.get(operationName)
    if (!startTime) {
      console.warn(`Timer "${operationName}" was not started`)
      return 0
    }

    const duration = performance.now() - startTime
    timers.current.delete(operationName)

    if (process.env.NODE_ENV === 'development') {
      console.log(`[Timer] ${operationName}: ${duration.toFixed(2)}ms`)
    }

    return duration
  }

  return { startTimer, endTimer }
}

// Hook for detecting slow renders
export const useSlowRenderDetector = (threshold: number = 16) => {
  const renderStartTime = useRef<number>(0)

  useEffect(() => {
    renderStartTime.current = performance.now()
  })

  useEffect(() => {
    const renderTime = performance.now() - renderStartTime.current
    
    if (renderTime > threshold && process.env.NODE_ENV === 'development') {
      console.warn(`[Slow Render] Component took ${renderTime.toFixed(2)}ms to render (threshold: ${threshold}ms)`)
    }
  })
}

// Performance observer for Web Vitals
export const useWebVitals = () => {
  const [vitals, setVitals] = useState<{
    CLS?: number
    FID?: number
    FCP?: number
    LCP?: number
    TTFB?: number
  }>({})

  useEffect(() => {
    if (!('PerformanceObserver' in window)) return

    // Largest Contentful Paint
    const lcpObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      const lastEntry = entries[entries.length - 1] as any
      setVitals(prev => ({ ...prev, LCP: lastEntry.startTime }))
    })

    // First Contentful Paint
    const fcpObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint') as any
      if (fcpEntry) {
        setVitals(prev => ({ ...prev, FCP: fcpEntry.startTime }))
      }
    })

    // Cumulative Layout Shift
    const clsObserver = new PerformanceObserver((list) => {
      let clsValue = 0
      for (const entry of list.getEntries() as any[]) {
        if (!entry.hadRecentInput) {
          clsValue += entry.value
        }
      }
      setVitals(prev => ({ ...prev, CLS: clsValue }))
    })

    try {
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
      fcpObserver.observe({ entryTypes: ['paint'] })
      clsObserver.observe({ entryTypes: ['layout-shift'] })
    } catch (error) {
      console.warn('Performance Observer not supported:', error)
    }

    return () => {
      lcpObserver.disconnect()
      fcpObserver.disconnect()
      clsObserver.disconnect()
    }
  }, [])

  return vitals
}
