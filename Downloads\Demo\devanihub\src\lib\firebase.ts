import { initializeApp } from 'firebase/app'
import { getFirestore, connectFirestoreEmulator } from 'firebase/firestore'
import { getStorage, connectStorageEmulator } from 'firebase/storage'

// Firebase configuration - In production, these should be environment variables
const firebaseConfig = {
  apiKey: "demo-api-key",
  authDomain: "devanihub-demo.firebaseapp.com",
  projectId: "devanihub-demo",
  storageBucket: "devanihub-demo.appspot.com",
  messagingSenderId: "123456789",
  appId: "1:123456789:web:abcdef123456"
}

// Initialize Firebase
const app = initializeApp(firebaseConfig)

// Initialize Firestore
export const db = getFirestore(app)

// Initialize Storage
export const storage = getStorage(app)

// For development, you can use the Firebase emulator
if (import.meta.env.DEV) {
  try {
    // Only connect to emulator if not already connected
    if (!db._delegate._databaseId.projectId.includes('localhost')) {
      connectFirestoreEmulator(db, 'localhost', 8080)
      connectStorageEmulator(storage, 'localhost', 9199)
    }
  } catch (error) {
    // Emulator connection might fail if not running, that's okay for demo
    console.log('Firebase emulator not available, using mock data')
  }
}

export default app
