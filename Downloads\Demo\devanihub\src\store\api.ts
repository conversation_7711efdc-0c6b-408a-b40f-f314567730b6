import { createApi, fakeBaseQuery } from '@reduxjs/toolkit/query/react'
import type { Anime, Episode, Tag, AnimeFormData, SearchFilters, PaginatedResponse } from '../types'
import * as firebaseService from '../lib/firebaseService'

// Mock data for development when Firebase is not available
const mockData: Anime[] = [
  {
    id: '1',
    title: 'Attack on Titan',
    season: 4,
    posterUrl: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=600&fit=crop',
    episodeCount: 24,
    tags: ['action', 'drama', 'military'],
    genre: 'Action',
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15'),
    episodes: Array.from({ length: 24 }, (_, i) => ({
      id: `ep-${i + 1}`,
      animeId: '1',
      episodeNumber: i + 1,
      title: `Episode ${i + 1}`,
      streamingUrl: `https://example.com/watch/aot-s4-ep${i + 1}`,
      thumbnailUrl: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=200&fit=crop',
      viewCount: Math.floor(Math.random() * 10000),
    })),
  },
  {
    id: '2',
    title: 'Demon Slayer',
    season: 3,
    posterUrl: 'https://images.unsplash.com/photo-1606918801925-e2c914c4b503?w=400&h=600&fit=crop',
    episodeCount: 12,
    tags: ['action', 'supernatural', 'shounen'],
    genre: 'Action',
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-10'),
    episodes: Array.from({ length: 12 }, (_, i) => ({
      id: `ds-ep-${i + 1}`,
      animeId: '2',
      episodeNumber: i + 1,
      title: `Episode ${i + 1}`,
      streamingUrl: `https://example.com/watch/ds-s3-ep${i + 1}`,
      thumbnailUrl: 'https://images.unsplash.com/photo-1606918801925-e2c914c4b503?w=300&h=200&fit=crop',
      viewCount: Math.floor(Math.random() * 8000),
    })),
  },
]

let mockTags: Tag[] = [
  { id: '1', name: 'action', usageCount: 2 },
  { id: '2', name: 'drama', usageCount: 1 },
  { id: '3', name: 'military', usageCount: 1 },
  { id: '4', name: 'supernatural', usageCount: 1 },
  { id: '5', name: 'shounen', usageCount: 1 },
]

export const animeApi = createApi({
  reducerPath: 'animeApi',
  baseQuery: fakeBaseQuery(),
  tagTypes: ['Anime', 'Episode', 'Tag'],
  endpoints: (builder) => ({
    // Anime endpoints
    getAnimes: builder.query<PaginatedResponse<Anime>, { page?: number; limit?: number; filters?: SearchFilters }>({
      queryFn: async ({ page = 1, limit = 12, filters }) => {
        try {
          // Try Firebase first
          const result = await firebaseService.getAnimes(filters, page, limit)
          return { data: result }
        } catch (error) {
          console.log('Firebase not available, using mock data')
          // Fallback to mock implementation
          let filteredData = [...mockData]

          if (filters?.query) {
            filteredData = filteredData.filter(anime =>
              anime.title.toLowerCase().includes(filters.query.toLowerCase()) ||
              anime.tags.some(tag => tag.toLowerCase().includes(filters.query.toLowerCase()))
            )
          }

          if (filters?.tags && filters.tags.length > 0) {
            filteredData = filteredData.filter(anime =>
              filters.tags.some(tag => anime.tags.includes(tag))
            )
          }

          if (filters?.genre) {
            filteredData = filteredData.filter(anime => anime.genre === filters.genre)
          }

          // Sort
          if (filters?.sortBy === 'alphabetical') {
            filteredData.sort((a, b) => a.title.localeCompare(b.title))
          } else if (filters?.sortBy === 'recent') {
            filteredData.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
          }

          const startIndex = (page - 1) * limit
          const endIndex = startIndex + limit
          const paginatedData = filteredData.slice(startIndex, endIndex)

          return {
            data: {
              data: paginatedData,
              total: filteredData.length,
              page,
              limit,
              hasMore: endIndex < filteredData.length,
            }
          }
        }
      },
      providesTags: ['Anime'],
    }),
    
    getAnimeById: builder.query<Anime, string>({
      queryFn: async (id) => {
        try {
          const anime = await firebaseService.getAnimeById(id)
          if (!anime) {
            return { error: { status: 404, data: 'Anime not found' } }
          }
          return { data: anime }
        } catch (error) {
          console.log('Firebase not available, using mock data')
          const anime = mockData.find(a => a.id === id)
          if (!anime) {
            return { error: { status: 404, data: 'Anime not found' } }
          }
          return { data: anime }
        }
      },
      providesTags: (result, error, id) => [{ type: 'Anime', id }],
    }),

    createAnime: builder.mutation<Anime, AnimeFormData>({
      queryFn: async (animeData) => {
        try {
          const newAnime = await firebaseService.createAnime(animeData)
          return { data: newAnime }
        } catch (error) {
          console.log('Firebase not available, using mock data')
          // Mock implementation
          const newAnime: Anime = {
            id: Date.now().toString(),
            ...animeData,
            createdAt: new Date(),
            updatedAt: new Date(),
            episodes: animeData.episodes.map((ep, index) => ({
              id: `${Date.now()}-ep-${index}`,
              animeId: Date.now().toString(),
              episodeNumber: ep.episodeNumber,
              title: ep.title,
              streamingUrl: ep.streamingUrl,
              thumbnailUrl: ep.thumbnailUrl,
              viewCount: 0,
            })),
          }

          mockData.push(newAnime)

          // Update tags
          animeData.tags.forEach(tagName => {
            const existingTag = mockTags.find(t => t.name === tagName)
            if (existingTag) {
              existingTag.usageCount++
            } else {
              mockTags.push({
                id: Date.now().toString() + Math.random(),
                name: tagName,
                usageCount: 1,
              })
            }
          })

          return { data: newAnime }
        }
      },
      invalidatesTags: ['Anime', 'Tag'],
    }),
    
    updateAnime: builder.mutation<Anime, { id: string; data: Partial<AnimeFormData> }>({
      queryFn: async ({ id, data }) => {
        try {
          const updatedAnime = await firebaseService.updateAnime(id, data)
          return { data: updatedAnime }
        } catch (error) {
          console.log('Firebase not available, using mock data')
          const animeIndex = mockData.findIndex(a => a.id === id)
          if (animeIndex === -1) {
            return { error: { status: 404, data: 'Anime not found' } }
          }

          mockData[animeIndex] = {
            ...mockData[animeIndex],
            ...data,
            updatedAt: new Date(),
          }

          return { data: mockData[animeIndex] }
        }
      },
      invalidatesTags: (result, error, { id }) => [{ type: 'Anime', id }],
    }),

    deleteAnime: builder.mutation<void, string>({
      queryFn: async (id) => {
        try {
          await firebaseService.deleteAnime(id)
          return { data: undefined }
        } catch (error) {
          console.log('Firebase not available, using mock data')
          const animeIndex = mockData.findIndex(a => a.id === id)
          if (animeIndex === -1) {
            return { error: { status: 404, data: 'Anime not found' } }
          }

          mockData.splice(animeIndex, 1)
          return { data: undefined }
        }
      },
      invalidatesTags: ['Anime'],
    }),

    // Tag endpoints
    getTags: builder.query<Tag[], void>({
      queryFn: async () => {
        try {
          const tags = await firebaseService.getTags()
          return { data: tags }
        } catch (error) {
          console.log('Firebase not available, using mock data')
          return { data: mockTags }
        }
      },
      providesTags: ['Tag'],
    }),

    // Episode endpoints
    updateEpisodeViewCount: builder.mutation<Episode, { animeId: string; episodeId: string }>({
      queryFn: async ({ animeId, episodeId }) => {
        try {
          await firebaseService.updateEpisodeViewCount(animeId, episodeId)
          // Return updated episode data
          const anime = await firebaseService.getAnimeById(animeId)
          const episode = anime?.episodes.find(ep => ep.id === episodeId)
          if (!episode) {
            return { error: { status: 404, data: 'Episode not found' } }
          }
          return { data: episode }
        } catch (error) {
          console.log('Firebase not available, using mock data')
          for (const anime of mockData) {
            const episode = anime.episodes.find(ep => ep.id === episodeId)
            if (episode) {
              episode.viewCount++
              return { data: episode }
            }
          }
          return { error: { status: 404, data: 'Episode not found' } }
        }
      },
      invalidatesTags: ['Anime'],
    }),
  }),
})

export const {
  useGetAnimesQuery,
  useGetAnimeByIdQuery,
  useCreateAnimeMutation,
  useUpdateAnimeMutation,
  useDeleteAnimeMutation,
  useGetTagsQuery,
  useUpdateEpisodeViewCountMutation,
} = animeApi
