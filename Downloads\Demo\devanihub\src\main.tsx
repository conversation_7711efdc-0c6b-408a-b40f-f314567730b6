import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.tsx'
import { register as registerSW, showUpdateAvailableNotification } from './utils/serviceWorker'

// Register service worker for offline support
registerSW({
  onSuccess: (registration) => {
    console.log('DevAniHub is now available offline!')
  },
  onUpdate: (registration) => {
    showUpdateAvailableNotification(registration)
  },
  onOffline: () => {
    console.log('DevAniHub is running in offline mode')
  },
  onOnline: () => {
    console.log('DevAniHub is back online')
  }
})

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <App />
  </StrictMode>,
)
