import * as React from "react"
import { useNavigate, useLocation } from "react-router-dom"
import { Home, Plus, Search, Menu, X, BarChart3 } from "lucide-react"
import { Button } from "../ui/Button"
import { cn } from "../../lib/utils"

interface MobileNavigationProps {
  className?: string
}

const MobileNavigation: React.FC<MobileNavigationProps> = ({ className }) => {
  const navigate = useNavigate()
  const location = useLocation()
  const [isMenuOpen, setIsMenuOpen] = React.useState(false)

  const navigationItems = [
    {
      icon: Home,
      label: "Home",
      path: "/",
      isActive: location.pathname === "/",
    },
    {
      icon: Plus,
      label: "Add Anime",
      path: "/add-anime",
      isActive: location.pathname === "/add-anime",
    },
    {
      icon: BarChart3,
      label: "Analytics",
      path: "/analytics",
      isActive: location.pathname === "/analytics",
    },
  ]

  const handleNavigation = (path: string) => {
    navigate(path)
    setIsMenuOpen(false)
  }

  return (
    <>
      {/* Mobile Bottom Navigation */}
      <div className={cn(
        "fixed bottom-0 left-0 right-0 z-50 bg-background border-t md:hidden",
        className
      )}>
        <div className="flex items-center justify-around py-2">
          {navigationItems.map((item) => {
            const Icon = item.icon
            return (
              <button
                key={item.path}
                onClick={() => handleNavigation(item.path)}
                className={cn(
                  "flex flex-col items-center justify-center p-2 rounded-lg transition-colors",
                  "min-w-[60px] min-h-[60px]",
                  item.isActive
                    ? "text-primary bg-primary/10"
                    : "text-muted-foreground hover:text-foreground hover:bg-accent"
                )}
              >
                <Icon className="h-5 w-5 mb-1" />
                <span className="text-xs font-medium">{item.label}</span>
              </button>
            )
          })}
          
          {/* Menu Button */}
          <button
            onClick={() => setIsMenuOpen(true)}
            className="flex flex-col items-center justify-center p-2 rounded-lg transition-colors min-w-[60px] min-h-[60px] text-muted-foreground hover:text-foreground hover:bg-accent"
          >
            <Menu className="h-5 w-5 mb-1" />
            <span className="text-xs font-medium">Menu</span>
          </button>
        </div>
      </div>

      {/* Mobile Menu Overlay */}
      {isMenuOpen && (
        <div className="fixed inset-0 z-50 md:hidden">
          {/* Backdrop */}
          <div 
            className="absolute inset-0 bg-black/50"
            onClick={() => setIsMenuOpen(false)}
          />
          
          {/* Menu Panel */}
          <div className="absolute right-0 top-0 h-full w-80 max-w-[85vw] bg-background border-l shadow-xl">
            <div className="flex items-center justify-between p-4 border-b">
              <h2 className="text-lg font-semibold">DevAniHub</h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsMenuOpen(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            
            <div className="p-4 space-y-2">
              {navigationItems.map((item) => {
                const Icon = item.icon
                return (
                  <button
                    key={item.path}
                    onClick={() => handleNavigation(item.path)}
                    className={cn(
                      "w-full flex items-center gap-3 p-3 rounded-lg transition-colors text-left",
                      item.isActive
                        ? "text-primary bg-primary/10"
                        : "text-foreground hover:bg-accent"
                    )}
                  >
                    <Icon className="h-5 w-5" />
                    <span className="font-medium">{item.label}</span>
                  </button>
                )
              })}
              
              <div className="border-t pt-4 mt-4">
                <div className="text-sm text-muted-foreground px-3 py-2">
                  Version 1.0.0
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Spacer for bottom navigation */}
      <div className="h-20 md:hidden" />
    </>
  )
}

export { MobileNavigation }
