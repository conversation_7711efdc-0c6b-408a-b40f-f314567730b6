import * as React from "react"
import { useNavigate } from "react-router-dom"
import { toast } from "react-hot-toast"
import { AddAnimeForm } from "../forms/AddAnimeForm"
import { Layout } from "../layout/Layout"
import { useCreateAnimeMutation } from "../../store/api"
import type { AnimeFormData } from "../../lib/validations"

const AddAnimePage: React.FC = () => {
  const navigate = useNavigate()
  const [createAnime, { isLoading }] = useCreateAnimeMutation()

  const handleSubmit = async (data: AnimeFormData) => {
    try {
      await createAnime(data).unwrap()
      toast.success("Anime created successfully!")
      navigate("/")
    } catch (error) {
      console.error("Error creating anime:", error)
      toast.error("Failed to create anime. Please try again.")
    }
  }

  return (
    <Layout>
      <div className="max-w-6xl mx-auto">
        <div className="mb-6">
          <h1 className="text-3xl font-bold">Add New Anime</h1>
          <p className="text-muted-foreground mt-2">
            Add a new anime series with episodes and streaming links
          </p>
        </div>

        <AddAnimeForm onSubmit={handleSubmit} isLoading={isLoading} />
      </div>
    </Layout>
  )
}

export { AddAnimePage }
