import { configureStore } from '@reduxjs/toolkit'
import { animeApi } from './api'
import uiSlice from './slices/uiSlice'

export const store = configureStore({
  reducer: {
    [animeApi.reducerPath]: animeApi.reducer,
    ui: uiSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [animeApi.util.getRunningQueriesThunk.fulfilled.type],
      },
    }).concat(animeApi.middleware),
})

export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch
