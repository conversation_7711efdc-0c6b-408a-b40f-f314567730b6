import React, { useState, useEffect, useRef, useMemo } from 'react'
import { cn } from '../../lib/utils'

interface VirtualGridProps<T> {
  items: T[]
  itemHeight: number
  itemWidth: number
  containerHeight: number
  gap?: number
  columns?: number
  renderItem: (item: T, index: number) => React.ReactNode
  className?: string
  overscan?: number
}

function VirtualGrid<T>({
  items,
  itemHeight,
  itemWidth,
  containerHeight,
  gap = 16,
  columns = 1,
  renderItem,
  className,
  overscan = 5
}: VirtualGridProps<T>) {
  const [scrollTop, setScrollTop] = useState(0)
  const scrollElementRef = useRef<HTMLDivElement>(null)

  const rowHeight = itemHeight + gap
  const totalRows = Math.ceil(items.length / columns)
  const totalHeight = totalRows * rowHeight

  // Calculate visible range
  const visibleRange = useMemo(() => {
    const visibleRowCount = Math.ceil(containerHeight / rowHeight)
    const startRow = Math.floor(scrollTop / rowHeight)
    const endRow = Math.min(startRow + visibleRowCount + overscan, totalRows)
    const startIndex = Math.max(0, startRow - overscan) * columns
    const endIndex = Math.min(endRow * columns, items.length)

    return {
      startIndex,
      endIndex,
      startRow: Math.max(0, startRow - overscan),
      endRow
    }
  }, [scrollTop, containerHeight, rowHeight, totalRows, columns, items.length, overscan])

  // Get visible items
  const visibleItems = useMemo(() => {
    return items.slice(visibleRange.startIndex, visibleRange.endIndex)
  }, [items, visibleRange.startIndex, visibleRange.endIndex])

  // Handle scroll
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop)
  }

  // Scroll to index
  const scrollToIndex = (index: number) => {
    if (scrollElementRef.current) {
      const row = Math.floor(index / columns)
      const scrollTop = row * rowHeight
      scrollElementRef.current.scrollTop = scrollTop
    }
  }

  return (
    <div
      ref={scrollElementRef}
      className={cn("overflow-auto", className)}
      style={{ height: containerHeight }}
      onScroll={handleScroll}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        <div
          style={{
            transform: `translateY(${visibleRange.startRow * rowHeight}px)`,
            display: 'grid',
            gridTemplateColumns: `repeat(${columns}, ${itemWidth}px)`,
            gap: `${gap}px`,
            justifyContent: 'center'
          }}
        >
          {visibleItems.map((item, index) => {
            const actualIndex = visibleRange.startIndex + index
            return (
              <div key={actualIndex} style={{ height: itemHeight }}>
                {renderItem(item, actualIndex)}
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )
}

// Hook for managing virtual grid state
export function useVirtualGrid<T>(items: T[], options: {
  itemHeight: number
  itemWidth: number
  containerHeight: number
  columns?: number
  gap?: number
}) {
  const [scrollTop, setScrollTop] = useState(0)
  const [isScrolling, setIsScrolling] = useState(false)
  const scrollTimeoutRef = useRef<NodeJS.Timeout>()

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop)
    setIsScrolling(true)

    // Clear existing timeout
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current)
    }

    // Set scrolling to false after scroll ends
    scrollTimeoutRef.current = setTimeout(() => {
      setIsScrolling(false)
    }, 150)
  }

  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current)
      }
    }
  }, [])

  return {
    scrollTop,
    isScrolling,
    handleScroll
  }
}

// Optimized list component for anime cards
interface VirtualAnimeGridProps<T> {
  items: T[]
  renderItem: (item: T, index: number) => React.ReactNode
  className?: string
  itemsPerRow?: number
  itemHeight?: number
  itemWidth?: number
  gap?: number
}

export function VirtualAnimeGrid<T>({
  items,
  renderItem,
  className,
  itemsPerRow = 5,
  itemHeight = 320,
  itemWidth = 200,
  gap = 16
}: VirtualAnimeGridProps<T>) {
  const containerRef = useRef<HTMLDivElement>(null)
  const [containerHeight, setContainerHeight] = useState(600)

  // Update container height on resize
  useEffect(() => {
    const updateHeight = () => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect()
        setContainerHeight(window.innerHeight - rect.top - 100) // Leave some margin
      }
    }

    updateHeight()
    window.addEventListener('resize', updateHeight)
    return () => window.removeEventListener('resize', updateHeight)
  }, [])

  // Responsive columns based on container width
  const columns = useMemo(() => {
    if (typeof window === 'undefined') return itemsPerRow
    
    const width = window.innerWidth
    if (width < 640) return 2
    if (width < 768) return 3
    if (width < 1024) return 4
    if (width < 1280) return 5
    return itemsPerRow
  }, [itemsPerRow])

  return (
    <div ref={containerRef} className={className}>
      <VirtualGrid
        items={items}
        itemHeight={itemHeight}
        itemWidth={itemWidth}
        containerHeight={containerHeight}
        columns={columns}
        gap={gap}
        renderItem={renderItem}
        overscan={3}
      />
    </div>
  )
}

export default VirtualGrid
