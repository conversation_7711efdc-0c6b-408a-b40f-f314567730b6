import * as React from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { Provider } from 'react-redux'
import { Toaster } from 'react-hot-toast'
import { store } from './store'
import HomePage from './components/HomePage'
import { AddAnimePage } from './components/pages/AddAnimePage'
import { EpisodeDetailsPage } from './components/pages/EpisodeDetailsPage'
import { EpisodePreviewModal } from './components/episodes/EpisodePreviewModal'
import { AnalyticsDashboard } from './components/analytics/AnalyticsDashboard'

function App() {
  const [modalState, setModalState] = React.useState<{
    isOpen: boolean
    animeId?: string
    episodeId?: string
  }>({ isOpen: false })

  // const openModal = (animeId: string, episodeId?: string) => {
  //   setModalState({ isOpen: true, animeId, episodeId })
  // }

  const closeModal = () => {
    setModalState({ isOpen: false })
  }

  return (
    <Provider store={store}>
      <Router>
        <div className="min-h-screen bg-background text-foreground">
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/add-anime" element={<AddAnimePage />} />
            <Route path="/analytics" element={<AnalyticsDashboard />} />
            <Route path="/anime/:animeId" element={<EpisodeDetailsPage />} />
            <Route path="/anime/:animeId/episode/:episodeId" element={<EpisodeDetailsPage />} />
          </Routes>

          {/* Global Modal */}
          {modalState.isOpen && modalState.animeId && (
            <EpisodePreviewModal
              animeId={modalState.animeId}
              episodeId={modalState.episodeId}
              isOpen={modalState.isOpen}
              onClose={closeModal}
            />
          )}

          {/* Toast Notifications */}
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: 'hsl(var(--background))',
                color: 'hsl(var(--foreground))',
                border: '1px solid hsl(var(--border))',
              },
            }}
          />
        </div>
      </Router>
    </Provider>
  )
}

export default App
