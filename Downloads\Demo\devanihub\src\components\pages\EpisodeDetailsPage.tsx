import * as React from "react"
import { useParams, useNavigate } from "react-router-dom"
import { ArrowLeft, Play, ExternalLink, Eye, Calendar, Share2 } from "lucide-react"
import { Layout } from "../layout/Layout"
import { Button } from "../ui/Button"
import { Badge } from "../ui/Badge"
import { LoadingSpinner } from "../ui/LoadingSpinner"
import { VirtualEpisodeList } from "../episodes/VirtualEpisodeList"
import { useGetAnimeByIdQuery, useUpdateEpisodeViewCountMutation } from "../../store/api"
import type { Episode } from "../../types"
import { cn } from "../../lib/utils"

const EpisodeDetailsPage: React.FC = () => {
  const { animeId, episodeId } = useParams<{ animeId: string; episodeId?: string }>()
  const navigate = useNavigate()
  const [selectedEpisode, setSelectedEpisode] = React.useState<Episode | null>(null)

  const { data: anime, isLoading, error } = useGetAnimeByIdQuery(animeId!, {
    skip: !animeId,
  })

  const [updateViewCount] = useUpdateEpisodeViewCountMutation()

  React.useEffect(() => {
    if (anime && anime.episodes.length > 0) {
      const episode = episodeId 
        ? anime.episodes.find(ep => ep.id === episodeId)
        : anime.episodes[0]
      setSelectedEpisode(episode || anime.episodes[0])
    }
  }, [anime, episodeId])

  const handleEpisodeSelect = (episode: Episode) => {
    setSelectedEpisode(episode)
    // Update URL without page reload
    navigate(`/anime/${animeId}/episode/${episode.id}`, { replace: true })
  }

  const handleEpisodePlay = async (episode: Episode) => {
    try {
      await updateViewCount({ animeId: animeId!, episodeId: episode.id })
      window.open(episode.streamingUrl, '_blank')
    } catch (error) {
      console.error('Failed to update view count:', error)
    }
  }

  const handleShare = async () => {
    if (selectedEpisode && navigator.share) {
      try {
        await navigator.share({
          title: `${anime?.title} - Episode ${selectedEpisode.episodeNumber}`,
          text: selectedEpisode.title,
          url: window.location.href,
        })
      } catch (error) {
        // Fallback to clipboard
        navigator.clipboard.writeText(window.location.href)
      }
    } else {
      // Fallback to clipboard
      navigator.clipboard.writeText(window.location.href)
    }
  }

  if (error) {
    return (
      <Layout>
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-destructive mb-2">Error Loading Anime</h2>
          <p className="text-muted-foreground mb-4">The anime you're looking for could not be found.</p>
          <Button onClick={() => navigate('/')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Home
          </Button>
        </div>
      </Layout>
    )
  }

  if (isLoading) {
    return (
      <Layout>
        <div className="flex justify-center items-center py-12">
          <div className="text-center">
            <LoadingSpinner size="lg" className="mx-auto mb-4" />
            <p className="text-muted-foreground">Loading anime details...</p>
          </div>
        </div>
      </Layout>
    )
  }

  if (!anime) {
    return (
      <Layout>
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold mb-2">Anime Not Found</h2>
          <p className="text-muted-foreground mb-4">The anime you're looking for does not exist.</p>
          <Button onClick={() => navigate('/')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Home
          </Button>
        </div>
      </Layout>
    )
  }

  return (
    <Layout>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <Button 
            variant="ghost" 
            onClick={() => navigate('/')}
            className="mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Home
          </Button>
          
          <div className="flex flex-col lg:flex-row gap-6">
            {/* Anime Poster */}
            {anime.posterUrl && (
              <div className="lg:w-64 flex-shrink-0">
                <img
                  src={anime.posterUrl}
                  alt={anime.title}
                  className="w-full h-auto rounded-lg shadow-lg"
                />
              </div>
            )}
            
            {/* Anime Info */}
            <div className="flex-1">
              <h1 className="text-3xl font-bold mb-2">{anime.title}</h1>
              <p className="text-lg text-muted-foreground mb-4">
                Season {anime.season} • {anime.episodeCount} Episodes • {anime.genre}
              </p>
              
              <div className="flex flex-wrap gap-2 mb-4">
                {anime.tags.map((tag) => (
                  <Badge key={tag} variant="outline">
                    {tag}
                  </Badge>
                ))}
              </div>
              
              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                <div className="flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  Added {new Date(anime.createdAt).toLocaleDateString()}
                </div>
                <div className="flex items-center gap-1">
                  <Eye className="h-4 w-4" />
                  {anime.episodes.reduce((total, ep) => total + ep.viewCount, 0).toLocaleString()} total views
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Episode Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Episode List */}
          <div className="lg:col-span-1">
            <h2 className="text-xl font-semibold mb-4">Episodes</h2>
            <VirtualEpisodeList
              episodes={anime.episodes}
              selectedEpisodeId={selectedEpisode?.id}
              onEpisodeSelect={handleEpisodeSelect}
              onEpisodePlay={handleEpisodePlay}
            />
          </div>

          {/* Episode Details */}
          <div className="lg:col-span-2">
            {selectedEpisode ? (
              <div className="space-y-6">
                {/* Episode Header */}
                <div className="bg-card rounded-lg p-6 border">
                  <div className="flex items-start gap-4">
                    {selectedEpisode.thumbnailUrl && (
                      <img
                        src={selectedEpisode.thumbnailUrl}
                        alt={selectedEpisode.title}
                        className="w-32 h-24 object-cover rounded-lg flex-shrink-0"
                      />
                    )}
                    <div className="flex-1">
                      <h3 className="text-2xl font-bold mb-2">
                        Episode {selectedEpisode.episodeNumber}: {selectedEpisode.title}
                      </h3>
                      
                      <div className="flex items-center gap-4 text-sm text-muted-foreground mb-4">
                        <div className="flex items-center gap-1">
                          <Eye className="h-4 w-4" />
                          {selectedEpisode.viewCount.toLocaleString()} views
                        </div>
                      </div>

                      <div className="flex flex-col sm:flex-row gap-3">
                        <Button 
                          onClick={() => handleEpisodePlay(selectedEpisode)}
                          disabled={!selectedEpisode.streamingUrl}
                          className="flex-1"
                        >
                          <Play className="h-4 w-4 mr-2" />
                          Watch Episode
                        </Button>
                        
                        <Button 
                          variant="outline"
                          onClick={() => window.open(selectedEpisode.streamingUrl, '_blank')}
                          disabled={!selectedEpisode.streamingUrl}
                        >
                          <ExternalLink className="h-4 w-4 mr-2" />
                          Open in New Tab
                        </Button>
                        
                        <Button variant="outline" onClick={handleShare}>
                          <Share2 className="h-4 w-4 mr-2" />
                          Share
                        </Button>
                      </div>

                      {!selectedEpisode.streamingUrl && (
                        <p className="text-sm text-muted-foreground mt-3">
                          Streaming URL not available for this episode
                        </p>
                      )}
                    </div>
                  </div>
                </div>

                {/* Episode Navigation */}
                <div className="flex justify-between">
                  <Button
                    variant="outline"
                    disabled={selectedEpisode.episodeNumber === 1}
                    onClick={() => {
                      const prevEpisode = anime.episodes.find(
                        ep => ep.episodeNumber === selectedEpisode.episodeNumber - 1
                      )
                      if (prevEpisode) handleEpisodeSelect(prevEpisode)
                    }}
                  >
                    Previous Episode
                  </Button>
                  
                  <Button
                    variant="outline"
                    disabled={selectedEpisode.episodeNumber === anime.episodeCount}
                    onClick={() => {
                      const nextEpisode = anime.episodes.find(
                        ep => ep.episodeNumber === selectedEpisode.episodeNumber + 1
                      )
                      if (nextEpisode) handleEpisodeSelect(nextEpisode)
                    }}
                  >
                    Next Episode
                  </Button>
                </div>
              </div>
            ) : (
              <div className="text-center py-12">
                <p className="text-muted-foreground">Select an episode to view details</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </Layout>
  )
}

export { EpisodeDetailsPage }
