# DevAniHub 🎌

A modern, responsive web application for managing anime episode streaming links with a beautiful UI and comprehensive features.

## ✨ Features

### Core Functionality
- **Anime Management**: Add, edit, and organize anime series with detailed metadata
- **Episode Tracking**: Manage episodes with streaming links, thumbnails, and view counts
- **Advanced Search**: Search by title, genre, tags with real-time filtering
- **Responsive Design**: Mobile-first approach with touch-friendly interactions
- **Analytics Dashboard**: Comprehensive insights into viewing patterns and popular content

### Technical Features
- **Progressive Web App (PWA)**: Offline support with service worker caching
- **Performance Optimized**: Lazy loading, virtual scrolling, and React.memo optimizations
- **Real-time Updates**: Firebase integration for live data synchronization
- **Modern UI**: Built with shadcn/ui components and Tailwind CSS
- **Type Safety**: Full TypeScript implementation with strict type checking

## 🚀 Tech Stack

- **Frontend**: React 18 + TypeScript + Vite
- **State Management**: Redux Toolkit + RTK Query
- **Styling**: Tailwind CSS + shadcn/ui components
- **Database**: Firebase Firestore + Storage
- **Routing**: React Router v6
- **Forms**: React Hook Form + Zod validation
- **Icons**: Lucide React
- **Testing**: Vitest + Testing Library

## 🛠️ Installation

### Prerequisites
- Node.js 18+
- npm or yarn
- Firebase project (for backend)

### Setup
1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd devanihub
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Configuration**
   Create a `.env` file in the root directory:
   ```env
   VITE_FIREBASE_API_KEY=your_api_key
   VITE_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
   VITE_FIREBASE_PROJECT_ID=your_project_id
   VITE_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
   VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
   VITE_FIREBASE_APP_ID=your_app_id
   ```

4. **Start development server**
   ```bash
   npm run dev
   ```

5. **Open in browser**
   Navigate to `http://localhost:5173`

## 🎯 Usage

### Adding Anime
1. Click the "Add Anime" button
2. Fill in the anime details (title, description, genre, etc.)
3. Upload a poster image
4. Add episodes with streaming links
5. Save to database

### Managing Episodes
- Add multiple episodes with different quality options
- Upload thumbnails for each episode
- Track view counts automatically
- Edit episode details anytime

### Analytics
- View comprehensive statistics on the Analytics page
- Track popular anime and genres
- Monitor viewing trends over time
- See recently added content

### Search and Filter
- Use the search bar for quick title searches
- Filter by genre, status, or custom tags
- Sort by various criteria (date, popularity, rating)

## 📦 Building for Production

```bash
# Build the application
npm run build

# Preview the build
npm run preview
```

## 🚀 Deployment

See [DEPLOYMENT.md](./DEPLOYMENT.md) for detailed deployment instructions including:
- Firebase Hosting
- Vercel
- Netlify
- CI/CD setup

## 🔧 Configuration

### Firebase Setup
1. Create a Firebase project
2. Enable Firestore and Storage
3. Configure security rules
4. Add your config to environment variables

### PWA Configuration
The app includes a service worker for offline functionality:
- Caches static assets
- Provides offline fallback pages
- Enables background sync
- Supports push notifications

## 📊 Performance

### Optimizations Implemented
- **Code Splitting**: Automatic route-based splitting
- **Lazy Loading**: Images and components load on demand
- **Virtual Scrolling**: Efficient rendering of large lists
- **Memoization**: React.memo and useMemo for expensive operations
- **Service Worker**: Aggressive caching for offline performance

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License.

## 🙏 Acknowledgments

- [shadcn/ui](https://ui.shadcn.com/) for the beautiful component library
- [Tailwind CSS](https://tailwindcss.com/) for the utility-first CSS framework
- [Firebase](https://firebase.google.com/) for the backend infrastructure
- [Lucide](https://lucide.dev/) for the icon set

---

**DevAniHub** - Built with ❤️ for anime enthusiasts
