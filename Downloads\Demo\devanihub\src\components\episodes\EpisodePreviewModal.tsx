import * as React from "react"
import { X, Play, Eye, Calendar, Clock, ExternalLink } from "lucide-react"
import { Button } from "../ui/Button"
import { Badge } from "../ui/Badge"
import { LoadingSpinner } from "../ui/LoadingSpinner"
import { useGetAnimeByIdQuery, useUpdateEpisodeViewCountMutation } from "../../store/api"
import type { Anime, Episode } from "../../types"
import { cn } from "../../lib/utils"

interface EpisodePreviewModalProps {
  animeId: string
  episodeId?: string
  isOpen: boolean
  onClose: () => void
}

const EpisodePreviewModal: React.FC<EpisodePreviewModalProps> = ({
  animeId,
  episodeId,
  isOpen,
  onClose,
}) => {
  const [selectedEpisode, setSelectedEpisode] = React.useState<Episode | null>(null)
  const [isPlaying, setIsPlaying] = React.useState(false)

  const { data: anime, isLoading } = useGetAnimeByIdQuery(animeId, {
    skip: !isOpen,
  })

  const [updateViewCount] = useUpdateEpisodeViewCountMutation()

  React.useEffect(() => {
    if (anime && anime.episodes.length > 0) {
      const episode = episodeId 
        ? anime.episodes.find(ep => ep.id === episodeId)
        : anime.episodes[0]
      setSelectedEpisode(episode || anime.episodes[0])
    }
  }, [anime, episodeId])

  const handleEpisodeSelect = (episode: Episode) => {
    setSelectedEpisode(episode)
    setIsPlaying(false)
  }

  const handlePlay = async () => {
    if (selectedEpisode) {
      setIsPlaying(true)
      try {
        await updateViewCount({ animeId, episodeId: selectedEpisode.id })
      } catch (error) {
        console.error('Failed to update view count:', error)
      }
      // Open streaming URL in new tab
      window.open(selectedEpisode.streamingUrl, '_blank')
    }
  }

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose()
    }
  }

  if (!isOpen) return null

  return (
    <div 
      className="fixed inset-0 z-50 bg-black/80 flex items-center justify-center p-4"
      onClick={handleBackdropClick}
    >
      <div className="bg-background rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <div>
            <h2 className="text-xl font-semibold">
              {anime?.title} - Season {anime?.season}
            </h2>
            <p className="text-sm text-muted-foreground">
              {anime?.episodeCount} episodes • {anime?.genre}
            </p>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        {isLoading ? (
          <div className="flex justify-center items-center py-12">
            <LoadingSpinner size="lg" />
          </div>
        ) : (
          <div className="flex flex-col lg:flex-row h-full">
            {/* Episode List */}
            <div className="lg:w-1/3 border-r">
              <div className="p-4 border-b">
                <h3 className="font-medium">Episodes</h3>
              </div>
              <div className="max-h-96 lg:max-h-[60vh] overflow-y-auto">
                {anime?.episodes.map((episode) => (
                  <button
                    key={episode.id}
                    onClick={() => handleEpisodeSelect(episode)}
                    className={cn(
                      "w-full p-4 text-left hover:bg-accent transition-colors border-b",
                      selectedEpisode?.id === episode.id && "bg-accent"
                    )}
                  >
                    <div className="flex items-start gap-3">
                      {episode.thumbnailUrl && (
                        <img
                          src={episode.thumbnailUrl}
                          alt={episode.title}
                          className="w-16 h-12 object-cover rounded flex-shrink-0"
                        />
                      )}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="text-sm font-medium">
                            Episode {episode.episodeNumber}
                          </span>
                          {selectedEpisode?.id === episode.id && (
                            <Badge variant="secondary" className="text-xs">
                              Selected
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-muted-foreground truncate">
                          {episode.title}
                        </p>
                        <div className="flex items-center gap-2 mt-1">
                          <Eye className="h-3 w-3" />
                          <span className="text-xs text-muted-foreground">
                            {episode.viewCount.toLocaleString()} views
                          </span>
                        </div>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* Episode Details */}
            <div className="lg:w-2/3 flex flex-col">
              {selectedEpisode ? (
                <>
                  {/* Episode Info */}
                  <div className="p-6 border-b">
                    <div className="flex items-start gap-4">
                      {selectedEpisode.thumbnailUrl && (
                        <img
                          src={selectedEpisode.thumbnailUrl}
                          alt={selectedEpisode.title}
                          className="w-32 h-24 object-cover rounded-lg flex-shrink-0"
                        />
                      )}
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold mb-2">
                          Episode {selectedEpisode.episodeNumber}: {selectedEpisode.title}
                        </h3>
                        
                        <div className="flex items-center gap-4 text-sm text-muted-foreground mb-4">
                          <div className="flex items-center gap-1">
                            <Eye className="h-4 w-4" />
                            {selectedEpisode.viewCount.toLocaleString()} views
                          </div>
                          <div className="flex items-center gap-1">
                            <Calendar className="h-4 w-4" />
                            {anime?.createdAt && new Date(anime.createdAt).toLocaleDateString()}
                          </div>
                        </div>

                        <div className="flex flex-wrap gap-2 mb-4">
                          {anime?.tags.map((tag) => (
                            <Badge key={tag} variant="outline" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="p-6">
                    <div className="flex flex-col sm:flex-row gap-4">
                      <Button 
                        onClick={handlePlay}
                        className="flex-1"
                        disabled={!selectedEpisode.streamingUrl}
                      >
                        <Play className="h-4 w-4 mr-2" />
                        {isPlaying ? "Opening..." : "Watch Episode"}
                      </Button>
                      
                      <Button 
                        variant="outline"
                        onClick={() => window.open(selectedEpisode.streamingUrl, '_blank')}
                        disabled={!selectedEpisode.streamingUrl}
                      >
                        <ExternalLink className="h-4 w-4 mr-2" />
                        Open in New Tab
                      </Button>
                    </div>

                    {!selectedEpisode.streamingUrl && (
                      <p className="text-sm text-muted-foreground mt-2">
                        Streaming URL not available for this episode
                      </p>
                    )}
                  </div>

                  {/* Episode Navigation */}
                  <div className="p-6 pt-0 mt-auto">
                    <div className="flex justify-between">
                      <Button
                        variant="outline"
                        disabled={selectedEpisode.episodeNumber === 1}
                        onClick={() => {
                          const prevEpisode = anime?.episodes.find(
                            ep => ep.episodeNumber === selectedEpisode.episodeNumber - 1
                          )
                          if (prevEpisode) handleEpisodeSelect(prevEpisode)
                        }}
                      >
                        Previous Episode
                      </Button>
                      
                      <Button
                        variant="outline"
                        disabled={selectedEpisode.episodeNumber === anime?.episodeCount}
                        onClick={() => {
                          const nextEpisode = anime?.episodes.find(
                            ep => ep.episodeNumber === selectedEpisode.episodeNumber + 1
                          )
                          if (nextEpisode) handleEpisodeSelect(nextEpisode)
                        }}
                      >
                        Next Episode
                      </Button>
                    </div>
                  </div>
                </>
              ) : (
                <div className="flex-1 flex items-center justify-center">
                  <p className="text-muted-foreground">Select an episode to view details</p>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export { EpisodePreviewModal }
