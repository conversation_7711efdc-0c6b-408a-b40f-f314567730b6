export interface Anime {
  id: string;
  title: string;
  season: number;
  posterUrl: string;
  episodeCount: number;
  tags: string[];
  genre: string;
  createdAt: Date;
  updatedAt: Date;
  episodes: Episode[];
}

export interface Episode {
  id: string;
  animeId: string;
  episodeNumber: number;
  title?: string;
  streamingUrl: string;
  thumbnailUrl?: string;
  viewCount: number;
}

export interface Tag {
  id: string;
  name: string;
  usageCount: number;
}

export interface AnimeFormData {
  title: string;
  season: number;
  posterUrl: string;
  posterFile?: File;
  episodeCount: number;
  tags: string[];
  genre: string;
  episodes: EpisodeFormData[];
}

export interface EpisodeFormData {
  episodeNumber: number;
  title?: string;
  streamingUrl: string;
  thumbnailUrl?: string;
  thumbnailFile?: File;
}

export interface SearchFilters {
  query: string;
  tags: string[];
  genre?: string;
  sortBy: 'recent' | 'popular' | 'alphabetical';
}

export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

export const ANIME_GENRES = [
  'Action',
  'Adventure',
  'Comedy',
  'Drama',
  'Fantasy',
  'Horror',
  'Mystery',
  'Romance',
  'Sci-Fi',
  'Slice of Life',
  'Sports',
  'Supernatural',
  'Thriller',
] as const;

export type AnimeGenre = typeof ANIME_GENRES[number];

export interface LoadingState {
  isLoading: boolean;
  error?: string;
}

export interface ModalState {
  isOpen: boolean;
  type?: 'add' | 'edit' | 'view' | 'delete';
  data?: any;
}
