import * as React from "react"
import { <PERSON>, <PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "../ui/Button"
import { Badge } from "../ui/Badge"
import type { Episode } from "../../types"
import { cn } from "../../lib/utils"

interface VirtualEpisodeListProps {
  episodes: Episode[]
  selectedEpisodeId?: string
  onEpisodeSelect: (episode: Episode) => void
  onEpisodePlay: (episode: Episode) => void
  className?: string
}

const ITEM_HEIGHT = 80
const CONTAINER_HEIGHT = 400

const VirtualEpisodeList: React.FC<VirtualEpisodeListProps> = ({
  episodes,
  selectedEpisodeId,
  onEpisodeSelect,
  onEpisodePlay,
  className,
}) => {
  const [scrollTop, setScrollTop] = React.useState(0)
  const containerRef = React.useRef<HTMLDivElement>(null)

  const visibleStart = Math.floor(scrollTop / ITEM_HEIGHT)
  const visibleEnd = Math.min(
    visibleStart + Math.ceil(CONTAINER_HEIGHT / ITEM_HEIGHT) + 1,
    episodes.length
  )

  const visibleItems = episodes.slice(visibleStart, visibleEnd)
  const totalHeight = episodes.length * ITEM_HEIGHT
  const offsetY = visibleStart * ITEM_HEIGHT

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop)
  }

  React.useEffect(() => {
    // Scroll to selected episode
    if (selectedEpisodeId && containerRef.current) {
      const selectedIndex = episodes.findIndex(ep => ep.id === selectedEpisodeId)
      if (selectedIndex !== -1) {
        const scrollPosition = selectedIndex * ITEM_HEIGHT
        containerRef.current.scrollTop = scrollPosition
        setScrollTop(scrollPosition)
      }
    }
  }, [selectedEpisodeId, episodes])

  return (
    <div className={cn("relative", className)}>
      <div
        ref={containerRef}
        className="overflow-auto border rounded-lg"
        style={{ height: CONTAINER_HEIGHT }}
        onScroll={handleScroll}
      >
        <div style={{ height: totalHeight, position: 'relative' }}>
          <div
            style={{
              transform: `translateY(${offsetY}px)`,
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
            }}
          >
            {visibleItems.map((episode, index) => {
              const actualIndex = visibleStart + index
              const isSelected = episode.id === selectedEpisodeId

              return (
                <div
                  key={episode.id}
                  className={cn(
                    "flex items-center gap-4 p-4 border-b hover:bg-accent/50 transition-colors cursor-pointer",
                    isSelected && "bg-accent"
                  )}
                  style={{ height: ITEM_HEIGHT }}
                  onClick={() => onEpisodeSelect(episode)}
                >
                  {/* Episode Number */}
                  <div className="flex-shrink-0 w-12 text-center">
                    <div className={cn(
                      "w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium",
                      isSelected 
                        ? "bg-primary text-primary-foreground" 
                        : "bg-muted text-muted-foreground"
                    )}>
                      {episode.episodeNumber}
                    </div>
                  </div>

                  {/* Thumbnail */}
                  {episode.thumbnailUrl && (
                    <div className="flex-shrink-0">
                      <img
                        src={episode.thumbnailUrl}
                        alt={episode.title}
                        className="w-16 h-12 object-cover rounded"
                        loading="lazy"
                      />
                    </div>
                  )}

                  {/* Episode Info */}
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium truncate mb-1">
                      {episode.title}
                    </h4>
                    <div className="flex items-center gap-3 text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Eye className="h-3 w-3" />
                        <span>{episode.viewCount.toLocaleString()}</span>
                      </div>
                      {episode.streamingUrl && (
                        <Badge variant="outline" className="text-xs">
                          Available
                        </Badge>
                      )}
                    </div>
                  </div>

                  {/* Play Button */}
                  <div className="flex-shrink-0">
                    <Button
                      size="sm"
                      variant={isSelected ? "default" : "outline"}
                      onClick={(e) => {
                        e.stopPropagation()
                        onEpisodePlay(episode)
                      }}
                      disabled={!episode.streamingUrl}
                    >
                      <Play className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      </div>

      {/* Episode Count Info */}
      <div className="mt-2 text-sm text-muted-foreground text-center">
        Showing {visibleStart + 1}-{Math.min(visibleEnd, episodes.length)} of {episodes.length} episodes
      </div>
    </div>
  )
}

export { VirtualEpisodeList }
