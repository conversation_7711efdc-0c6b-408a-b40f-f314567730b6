import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  DocumentSnapshot,
  Timestamp,
} from 'firebase/firestore'
import { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage'
import { db, storage } from './firebase'
import type { Anime, Episode, Tag, AnimeFormData, SearchFilters, PaginatedResponse } from '../types'
import { generateId } from './utils'

// Collections
const ANIME_COLLECTION = 'animes'
const EPISODES_COLLECTION = 'episodes'
const TAGS_COLLECTION = 'tags'

// Helper function to convert Firestore timestamp to Date
const convertTimestamp = (timestamp: any): Date => {
  if (timestamp?.toDate) {
    return timestamp.toDate()
  }
  return new Date(timestamp)
}

// Helper function to convert Anime data from Firestore
const convertAnimeData = (doc: any): Anime => ({
  id: doc.id,
  title: doc.data().title,
  season: doc.data().season,
  posterUrl: doc.data().posterUrl,
  episodeCount: doc.data().episodeCount,
  tags: doc.data().tags || [],
  genre: doc.data().genre,
  createdAt: convertTimestamp(doc.data().createdAt),
  updatedAt: convertTimestamp(doc.data().updatedAt),
  episodes: doc.data().episodes || [],
})

// Upload image to Firebase Storage
export const uploadImage = async (file: File, path: string): Promise<string> => {
  const storageRef = ref(storage, path)
  const snapshot = await uploadBytes(storageRef, file)
  return await getDownloadURL(snapshot.ref)
}

// Delete image from Firebase Storage
export const deleteImage = async (url: string): Promise<void> => {
  try {
    const imageRef = ref(storage, url)
    await deleteObject(imageRef)
  } catch (error) {
    console.error('Error deleting image:', error)
  }
}

// Anime CRUD operations
export const getAnimes = async (filters?: SearchFilters, page = 1, pageLimit = 12): Promise<PaginatedResponse<Anime>> => {
  try {
    let q = query(collection(db, ANIME_COLLECTION))
    
    // Apply filters
    if (filters?.genre) {
      q = query(q, where('genre', '==', filters.genre))
    }
    
    if (filters?.tags && filters.tags.length > 0) {
      q = query(q, where('tags', 'array-contains-any', filters.tags))
    }
    
    // Apply sorting
    if (filters?.sortBy === 'alphabetical') {
      q = query(q, orderBy('title'))
    } else if (filters?.sortBy === 'recent') {
      q = query(q, orderBy('createdAt', 'desc'))
    } else {
      q = query(q, orderBy('updatedAt', 'desc'))
    }
    
    // Apply pagination
    q = query(q, limit(pageLimit))
    
    const snapshot = await getDocs(q)
    const animes = snapshot.docs.map(convertAnimeData)
    
    // Filter by search query (client-side for simplicity)
    let filteredAnimes = animes
    if (filters?.query) {
      const searchQuery = filters.query.toLowerCase()
      filteredAnimes = animes.filter(anime =>
        anime.title.toLowerCase().includes(searchQuery) ||
        anime.tags.some(tag => tag.toLowerCase().includes(searchQuery))
      )
    }
    
    return {
      data: filteredAnimes,
      total: filteredAnimes.length,
      page,
      limit: pageLimit,
      hasMore: snapshot.docs.length === pageLimit,
    }
  } catch (error) {
    console.error('Error fetching animes:', error)
    throw error
  }
}

export const getAnimeById = async (id: string): Promise<Anime | null> => {
  try {
    const docRef = doc(db, ANIME_COLLECTION, id)
    const docSnap = await getDoc(docRef)
    
    if (docSnap.exists()) {
      return convertAnimeData(docSnap)
    }
    return null
  } catch (error) {
    console.error('Error fetching anime:', error)
    throw error
  }
}

export const createAnime = async (animeData: AnimeFormData): Promise<Anime> => {
  try {
    // Upload poster image if provided
    let posterUrl = animeData.posterUrl
    if (animeData.posterFile) {
      posterUrl = await uploadImage(animeData.posterFile, `posters/${generateId()}_${animeData.posterFile.name}`)
    }
    
    // Upload episode thumbnails if provided
    const episodes = await Promise.all(
      animeData.episodes.map(async (ep, index) => {
        let thumbnailUrl = ep.thumbnailUrl
        if (ep.thumbnailFile) {
          thumbnailUrl = await uploadImage(
            ep.thumbnailFile,
            `thumbnails/${generateId()}_${ep.thumbnailFile.name}`
          )
        }
        
        return {
          id: generateId(),
          animeId: '', // Will be set after anime creation
          episodeNumber: ep.episodeNumber,
          title: ep.title,
          streamingUrl: ep.streamingUrl,
          thumbnailUrl,
          viewCount: 0,
        }
      })
    )
    
    const now = Timestamp.now()
    const animeDoc = {
      title: animeData.title,
      season: animeData.season,
      posterUrl,
      episodeCount: animeData.episodeCount,
      tags: animeData.tags,
      genre: animeData.genre,
      createdAt: now,
      updatedAt: now,
      episodes,
    }
    
    const docRef = await addDoc(collection(db, ANIME_COLLECTION), animeDoc)
    
    // Update episode animeId references
    const updatedEpisodes = episodes.map(ep => ({ ...ep, animeId: docRef.id }))
    await updateDoc(docRef, { episodes: updatedEpisodes })
    
    // Update tag usage counts
    await updateTagUsage(animeData.tags, 1)
    
    return {
      id: docRef.id,
      ...animeDoc,
      episodes: updatedEpisodes,
      createdAt: now.toDate(),
      updatedAt: now.toDate(),
    }
  } catch (error) {
    console.error('Error creating anime:', error)
    throw error
  }
}

export const updateAnime = async (id: string, animeData: Partial<AnimeFormData>): Promise<Anime> => {
  try {
    const docRef = doc(db, ANIME_COLLECTION, id)
    const updateData: any = {
      ...animeData,
      updatedAt: Timestamp.now(),
    }
    
    // Handle poster image update
    if (animeData.posterFile) {
      updateData.posterUrl = await uploadImage(animeData.posterFile, `posters/${generateId()}_${animeData.posterFile.name}`)
    }
    
    await updateDoc(docRef, updateData)
    
    const updatedDoc = await getDoc(docRef)
    return convertAnimeData(updatedDoc)
  } catch (error) {
    console.error('Error updating anime:', error)
    throw error
  }
}

export const deleteAnime = async (id: string): Promise<void> => {
  try {
    const docRef = doc(db, ANIME_COLLECTION, id)
    const docSnap = await getDoc(docRef)
    
    if (docSnap.exists()) {
      const animeData = docSnap.data()
      
      // Delete associated images
      if (animeData.posterUrl) {
        await deleteImage(animeData.posterUrl)
      }
      
      if (animeData.episodes) {
        for (const episode of animeData.episodes) {
          if (episode.thumbnailUrl) {
            await deleteImage(episode.thumbnailUrl)
          }
        }
      }
      
      // Update tag usage counts
      if (animeData.tags) {
        await updateTagUsage(animeData.tags, -1)
      }
    }
    
    await deleteDoc(docRef)
  } catch (error) {
    console.error('Error deleting anime:', error)
    throw error
  }
}

// Tag operations
export const getTags = async (): Promise<Tag[]> => {
  try {
    const q = query(collection(db, TAGS_COLLECTION), orderBy('usageCount', 'desc'))
    const snapshot = await getDocs(q)
    
    return snapshot.docs.map(doc => ({
      id: doc.id,
      name: doc.data().name,
      usageCount: doc.data().usageCount,
    }))
  } catch (error) {
    console.error('Error fetching tags:', error)
    throw error
  }
}

const updateTagUsage = async (tags: string[], increment: number): Promise<void> => {
  try {
    for (const tagName of tags) {
      const q = query(collection(db, TAGS_COLLECTION), where('name', '==', tagName))
      const snapshot = await getDocs(q)
      
      if (snapshot.empty) {
        // Create new tag
        await addDoc(collection(db, TAGS_COLLECTION), {
          name: tagName,
          usageCount: Math.max(0, increment),
        })
      } else {
        // Update existing tag
        const tagDoc = snapshot.docs[0]
        const currentCount = tagDoc.data().usageCount || 0
        const newCount = Math.max(0, currentCount + increment)
        
        if (newCount === 0) {
          await deleteDoc(tagDoc.ref)
        } else {
          await updateDoc(tagDoc.ref, { usageCount: newCount })
        }
      }
    }
  } catch (error) {
    console.error('Error updating tag usage:', error)
  }
}

// Episode operations
export const updateEpisodeViewCount = async (animeId: string, episodeId: string): Promise<void> => {
  try {
    const animeRef = doc(db, ANIME_COLLECTION, animeId)
    const animeSnap = await getDoc(animeRef)
    
    if (animeSnap.exists()) {
      const animeData = animeSnap.data()
      const episodes = animeData.episodes || []
      
      const updatedEpisodes = episodes.map((ep: Episode) =>
        ep.id === episodeId ? { ...ep, viewCount: ep.viewCount + 1 } : ep
      )
      
      await updateDoc(animeRef, { episodes: updatedEpisodes })
    }
  } catch (error) {
    console.error('Error updating episode view count:', error)
    throw error
  }
}
