import { z } from 'zod'

// Episode validation schema
export const episodeSchema = z.object({
  episodeNumber: z.number().min(1, 'Episode number must be at least 1'),
  title: z.string().min(1, 'Episode title is required'),
  streamingUrl: z.string().url('Must be a valid URL'),
  thumbnailUrl: z.string().url('Must be a valid URL').optional().or(z.literal('')),
  thumbnailFile: z.instanceof(File).optional(),
})

// Anime form validation schema
export const animeFormSchema = z.object({
  title: z.string().min(1, 'Title is required').max(100, 'Title must be less than 100 characters'),
  season: z.number().min(1, 'Season must be at least 1').max(50, 'Season must be less than 50'),
  episodeCount: z.number().min(1, 'Episode count must be at least 1').max(1000, 'Episode count must be less than 1000'),
  genre: z.string().min(1, 'Genre is required'),
  tags: z.array(z.string()).min(1, 'At least one tag is required').max(10, 'Maximum 10 tags allowed'),
  posterUrl: z.string().url('Must be a valid URL').optional().or(z.literal('')),
  posterFile: z.instanceof(File).optional(),
  episodes: z.array(episodeSchema).min(1, 'At least one episode is required'),
}).refine((data) => {
  return data.episodes.length <= data.episodeCount
}, {
  message: 'Number of episodes cannot exceed episode count',
  path: ['episodes'],
})

// Search filters validation schema
export const searchFiltersSchema = z.object({
  query: z.string().optional(),
  genre: z.string().optional(),
  tags: z.array(z.string()).optional(),
  sortBy: z.enum(['recent', 'alphabetical', 'popular']).optional(),
})

export type EpisodeFormData = z.infer<typeof episodeSchema>
export type AnimeFormData = z.infer<typeof animeFormSchema>
export type SearchFiltersData = z.infer<typeof searchFiltersSchema>
