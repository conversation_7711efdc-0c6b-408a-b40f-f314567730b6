<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DevAniHub - Offline</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .container {
            text-align: center;
            padding: 2rem;
            max-width: 500px;
        }
        
        .icon {
            width: 120px;
            height: 120px;
            margin: 0 auto 2rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }
        
        p {
            font-size: 1.1rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            line-height: 1.6;
        }
        
        .button {
            display: inline-block;
            padding: 12px 24px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }
        
        .button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .features {
            margin-top: 3rem;
            text-align: left;
        }
        
        .feature {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
            padding: 0.5rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
        }
        
        .feature-icon {
            width: 24px;
            height: 24px;
            margin-right: 12px;
            opacity: 0.8;
        }
        
        @media (max-width: 640px) {
            .container {
                padding: 1rem;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .icon {
                width: 80px;
                height: 80px;
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">
            📱
        </div>
        
        <h1>You're Offline</h1>
        
        <p>
            Don't worry! DevAniHub works offline too. 
            Some features are available even without an internet connection.
        </p>
        
        <a href="/" class="button" onclick="window.location.reload()">
            Try Again
        </a>
        
        <div class="features">
            <div class="feature">
                <div class="feature-icon">💾</div>
                <div>Previously viewed anime are cached</div>
            </div>
            
            <div class="feature">
                <div class="feature-icon">🔍</div>
                <div>Search through cached content</div>
            </div>
            
            <div class="feature">
                <div class="feature-icon">📊</div>
                <div>View offline analytics</div>
            </div>
            
            <div class="feature">
                <div class="feature-icon">🔄</div>
                <div>Changes sync when back online</div>
            </div>
        </div>
    </div>
    
    <script>
        // Check for network connectivity
        function checkOnlineStatus() {
            if (navigator.onLine) {
                window.location.href = '/'
            }
        }
        
        // Listen for online event
        window.addEventListener('online', checkOnlineStatus)
        
        // Check periodically
        setInterval(checkOnlineStatus, 5000)
        
        // Service worker registration check
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.ready.then((registration) => {
                console.log('Service Worker is ready')
            })
        }
    </script>
</body>
</html>
