import * as React from "react"
import { useGetAnimesQuery } from "../../store/api"
import { Card } from "../ui/Card"
import { Badge } from "../ui/Badge"
import { LoadingSpinner } from "../ui/LoadingSpinner"
import { Eye, Play, TrendingUp, Calendar, Star, Users } from "lucide-react"
import type { Anime } from "../../types"

interface AnalyticsData {
  totalAnimes: number
  totalEpisodes: number
  totalViews: number
  mostPopularAnime: Anime | null
  recentlyAdded: Anime[]
  topGenres: { genre: string; count: number }[]
  viewsThisMonth: number
  averageRating: number
}

const AnalyticsDashboard: React.FC = () => {
  const { data: animesData, isLoading, error } = useGetAnimesQuery({})

  const analyticsData = React.useMemo((): AnalyticsData => {
    if (!animesData?.data) {
      return {
        totalAnimes: 0,
        totalEpisodes: 0,
        totalViews: 0,
        mostPopularAnime: null,
        recentlyAdded: [],
        topGenres: [],
        viewsThisMonth: 0,
        averageRating: 0,
      }
    }

    const animes = animesData.data
    const totalAnimes = animes.length
    const totalEpisodes = animes.reduce((sum, anime) => sum + anime.episodeCount, 0)
    const totalViews = animes.reduce((sum, anime) => 
      sum + anime.episodes.reduce((epSum, ep) => epSum + ep.viewCount, 0), 0
    )

    // Find most popular anime by total views
    const mostPopularAnime = animes.reduce((prev, current) => {
      const prevViews = prev.episodes.reduce((sum, ep) => sum + ep.viewCount, 0)
      const currentViews = current.episodes.reduce((sum, ep) => sum + ep.viewCount, 0)
      return currentViews > prevViews ? current : prev
    }, animes[0])

    // Recently added (last 7 days)
    const sevenDaysAgo = new Date()
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)
    const recentlyAdded = animes
      .filter(anime => new Date(anime.createdAt) > sevenDaysAgo)
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, 5)

    // Top genres
    const genreCounts: Record<string, number> = {}
    animes.forEach(anime => {
      genreCounts[anime.genre] = (genreCounts[anime.genre] || 0) + 1
    })
    const topGenres = Object.entries(genreCounts)
      .map(([genre, count]) => ({ genre, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5)

    // Views this month (mock calculation)
    const viewsThisMonth = Math.floor(totalViews * 0.3) // Assume 30% of views are from this month

    // Average rating (mock - would come from user ratings)
    const averageRating = 4.2

    return {
      totalAnimes,
      totalEpisodes,
      totalViews,
      mostPopularAnime,
      recentlyAdded,
      topGenres,
      viewsThisMonth,
      averageRating,
    }
  }, [animesData])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <LoadingSpinner />
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center p-8">
        <p className="text-destructive">Failed to load analytics data</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Analytics Dashboard</h2>
        <Badge variant="outline" className="text-xs">
          Last updated: {new Date().toLocaleDateString()}
        </Badge>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              <Play className="h-5 w-5 text-primary" />
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Total Animes</p>
              <p className="text-2xl font-bold">{analyticsData.totalAnimes}</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-500/10 rounded-lg">
              <Calendar className="h-5 w-5 text-blue-500" />
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Episodes</p>
              <p className="text-2xl font-bold">{analyticsData.totalEpisodes}</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-500/10 rounded-lg">
              <Eye className="h-5 w-5 text-green-500" />
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Total Views</p>
              <p className="text-2xl font-bold">{analyticsData.totalViews.toLocaleString()}</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-yellow-500/10 rounded-lg">
              <Star className="h-5 w-5 text-yellow-500" />
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Avg Rating</p>
              <p className="text-2xl font-bold">{analyticsData.averageRating}</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Charts and Details */}
      <div className="grid md:grid-cols-2 gap-6">
        {/* Most Popular Anime */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Most Popular Anime
          </h3>
          {analyticsData.mostPopularAnime ? (
            <div className="flex items-center gap-4">
              <div className="w-16 h-20 bg-muted rounded-md flex items-center justify-center">
                {analyticsData.mostPopularAnime.posterUrl ? (
                  <img
                    src={analyticsData.mostPopularAnime.posterUrl}
                    alt={analyticsData.mostPopularAnime.title}
                    className="w-full h-full object-cover rounded-md"
                  />
                ) : (
                  <Play className="h-6 w-6 text-muted-foreground" />
                )}
              </div>
              <div className="flex-1">
                <h4 className="font-medium line-clamp-2">{analyticsData.mostPopularAnime.title}</h4>
                <p className="text-sm text-muted-foreground">{analyticsData.mostPopularAnime.genre}</p>
                <div className="flex items-center gap-2 mt-2">
                  <Eye className="h-4 w-4" />
                  <span className="text-sm">
                    {analyticsData.mostPopularAnime.episodes
                      .reduce((sum, ep) => sum + ep.viewCount, 0)
                      .toLocaleString()} views
                  </span>
                </div>
              </div>
            </div>
          ) : (
            <p className="text-muted-foreground">No data available</p>
          )}
        </Card>

        {/* Top Genres */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Top Genres</h3>
          <div className="space-y-3">
            {analyticsData.topGenres.map((item, index) => (
              <div key={item.genre} className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">#{index + 1}</span>
                  <span className="text-sm">{item.genre}</span>
                </div>
                <Badge variant="secondary">{item.count}</Badge>
              </div>
            ))}
          </div>
        </Card>
      </div>

      {/* Recently Added */}
      {analyticsData.recentlyAdded.length > 0 && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Recently Added
          </h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
            {analyticsData.recentlyAdded.map((anime) => (
              <div key={anime.id} className="text-center">
                <div className="w-full aspect-[3/4] bg-muted rounded-md mb-2 flex items-center justify-center">
                  {anime.posterUrl ? (
                    <img
                      src={anime.posterUrl}
                      alt={anime.title}
                      className="w-full h-full object-cover rounded-md"
                    />
                  ) : (
                    <Play className="h-8 w-8 text-muted-foreground" />
                  )}
                </div>
                <h4 className="text-sm font-medium line-clamp-2">{anime.title}</h4>
                <p className="text-xs text-muted-foreground">
                  {new Date(anime.createdAt).toLocaleDateString()}
                </p>
              </div>
            ))}
          </div>
        </Card>
      )}
    </div>
  )
}

export { AnalyticsDashboard }
