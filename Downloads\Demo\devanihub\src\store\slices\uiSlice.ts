import { createSlice, type PayloadAction } from '@reduxjs/toolkit'
import type { ModalState, SearchFilters } from '../../types'

interface UiState {
  modal: ModalState
  searchFilters: SearchFilters
  theme: 'light' | 'dark'
  sidebarOpen: boolean
}

const initialState: UiState = {
  modal: {
    isOpen: false,
  },
  searchFilters: {
    query: '',
    tags: [],
    sortBy: 'recent',
  },
  theme: 'light',
  sidebarOpen: false,
}

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    openModal: (state, action: PayloadAction<{ type: ModalState['type']; data?: any }>) => {
      state.modal = {
        isOpen: true,
        type: action.payload.type,
        data: action.payload.data,
      }
    },
    closeModal: (state) => {
      state.modal = {
        isOpen: false,
      }
    },
    updateSearchFilters: (state, action: PayloadAction<Partial<SearchFilters>>) => {
      state.searchFilters = {
        ...state.searchFilters,
        ...action.payload,
      }
    },
    clearSearchFilters: (state) => {
      state.searchFilters = initialState.searchFilters
    },
    toggleTheme: (state) => {
      state.theme = state.theme === 'light' ? 'dark' : 'light'
    },
    setTheme: (state, action: PayloadAction<'light' | 'dark'>) => {
      state.theme = action.payload
    },
    toggleSidebar: (state) => {
      state.sidebarOpen = !state.sidebarOpen
    },
    setSidebarOpen: (state, action: PayloadAction<boolean>) => {
      state.sidebarOpen = action.payload
    },
  },
})

export const {
  openModal,
  closeModal,
  updateSearchFilters,
  clearSearchFilters,
  toggleTheme,
  setTheme,
  toggleSidebar,
  setSidebarOpen,
} = uiSlice.actions

export default uiSlice.reducer
