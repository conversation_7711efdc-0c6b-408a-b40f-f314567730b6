import * as React from "react"
import { useForm, useFieldArray } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { Plus, Trash2, Upload, X } from "lucide-react"
import { animeFormSchema, type AnimeFormData } from "../../lib/validations"
import { ANIME_GENRES } from "../../types"
import { Button } from "../ui/Button"
import { Input } from "../ui/Input"
import { Label } from "../ui/Label"
import { Textarea } from "../ui/Textarea"
import { Select } from "../ui/Select"
import { MultiSelect } from "../ui/MultiSelect"
import { Card } from "../ui/Card"
import { LoadingSpinner } from "../ui/LoadingSpinner"
import { cn } from "../../lib/utils"

interface AddAnimeFormProps {
  onSubmit: (data: AnimeFormData) => Promise<void>
  isLoading?: boolean
  initialData?: Partial<AnimeFormData>
}

const AddAnimeForm: React.FC<AddAnimeFormProps> = ({
  onSubmit,
  isLoading = false,
  initialData,
}) => {
  const [currentStep, setCurrentStep] = React.useState(1)
  const [posterPreview, setPosterPreview] = React.useState<string | null>(null)
  const [episodeThumbnails, setEpisodeThumbnails] = React.useState<{ [key: number]: string }>({})

  const {
    register,
    control,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
    trigger,
  } = useForm<AnimeFormData>({
    resolver: zodResolver(animeFormSchema),
    defaultValues: {
      title: initialData?.title || "",
      season: initialData?.season || 1,
      episodeCount: initialData?.episodeCount || 1,
      genre: initialData?.genre || "",
      tags: initialData?.tags || [],
      posterUrl: initialData?.posterUrl || "",
      episodes: initialData?.episodes || [
        {
          episodeNumber: 1,
          title: "",
          streamingUrl: "",
          thumbnailUrl: "",
        },
      ],
    },
  })

  const { fields, append, remove } = useFieldArray({
    control,
    name: "episodes",
  })

  const watchedEpisodeCount = watch("episodeCount")
  const watchedTags = watch("tags")

  // Handle poster file upload
  const handlePosterUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      setValue("posterFile", file)
      const reader = new FileReader()
      reader.onload = (e) => setPosterPreview(e.target?.result as string)
      reader.readAsDataURL(file)
    }
  }

  // Handle episode thumbnail upload
  const handleEpisodeThumbnailUpload = (index: number, event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      setValue(`episodes.${index}.thumbnailFile`, file)
      const reader = new FileReader()
      reader.onload = (e) => {
        setEpisodeThumbnails(prev => ({
          ...prev,
          [index]: e.target?.result as string
        }))
      }
      reader.readAsDataURL(file)
    }
  }

  // Auto-generate episodes based on episode count
  React.useEffect(() => {
    const currentEpisodeCount = fields.length
    const targetCount = watchedEpisodeCount

    if (targetCount > currentEpisodeCount) {
      // Add episodes
      for (let i = currentEpisodeCount; i < targetCount; i++) {
        append({
          episodeNumber: i + 1,
          title: `Episode ${i + 1}`,
          streamingUrl: "",
          thumbnailUrl: "",
        })
      }
    } else if (targetCount < currentEpisodeCount) {
      // Remove episodes
      for (let i = currentEpisodeCount - 1; i >= targetCount; i--) {
        remove(i)
      }
    }
  }, [watchedEpisodeCount, fields.length, append, remove])

  const nextStep = async () => {
    const fieldsToValidate = currentStep === 1 
      ? ['title', 'season', 'episodeCount', 'genre', 'tags'] 
      : ['episodes']
    
    const isValid = await trigger(fieldsToValidate as any)
    if (isValid) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    setCurrentStep(currentStep - 1)
  }

  const onFormSubmit = async (data: AnimeFormData) => {
    try {
      await onSubmit(data)
    } catch (error) {
      console.error('Form submission error:', error)
    }
  }

  const genreOptions = ANIME_GENRES.map(genre => ({ value: genre, label: genre }))

  return (
    <div className="max-w-4xl mx-auto">
      {/* Progress Steps */}
      <div className="mb-8">
        <div className="flex items-center justify-center space-x-4">
          {[1, 2, 3].map((step) => (
            <div key={step} className="flex items-center">
              <div
                className={cn(
                  "flex h-8 w-8 items-center justify-center rounded-full text-sm font-medium",
                  step <= currentStep
                    ? "bg-primary text-primary-foreground"
                    : "bg-muted text-muted-foreground"
                )}
              >
                {step}
              </div>
              {step < 3 && (
                <div
                  className={cn(
                    "h-0.5 w-16 mx-2",
                    step < currentStep ? "bg-primary" : "bg-muted"
                  )}
                />
              )}
            </div>
          ))}
        </div>
        <div className="flex justify-center mt-2">
          <span className="text-sm text-muted-foreground">
            Step {currentStep} of 3: {
              currentStep === 1 ? "Basic Information" :
              currentStep === 2 ? "Episodes" : "Review & Submit"
            }
          </span>
        </div>
      </div>

      <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-6">
        {/* Step 1: Basic Information */}
        {currentStep === 1 && (
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">Basic Information</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="md:col-span-2">
                <Label htmlFor="title">Anime Title *</Label>
                <Input
                  id="title"
                  {...register("title")}
                  placeholder="Enter anime title"
                  className={errors.title ? "border-destructive" : ""}
                />
                {errors.title && (
                  <p className="text-sm text-destructive mt-1">{errors.title.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="season">Season *</Label>
                <Input
                  id="season"
                  type="number"
                  min="1"
                  {...register("season", { valueAsNumber: true })}
                  className={errors.season ? "border-destructive" : ""}
                />
                {errors.season && (
                  <p className="text-sm text-destructive mt-1">{errors.season.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="episodeCount">Episode Count *</Label>
                <Input
                  id="episodeCount"
                  type="number"
                  min="1"
                  {...register("episodeCount", { valueAsNumber: true })}
                  className={errors.episodeCount ? "border-destructive" : ""}
                />
                {errors.episodeCount && (
                  <p className="text-sm text-destructive mt-1">{errors.episodeCount.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="genre">Genre *</Label>
                <Select
                  options={genreOptions}
                  value={watch("genre")}
                  onValueChange={(value) => setValue("genre", value)}
                  placeholder="Select genre"
                  className={errors.genre ? "border-destructive" : ""}
                />
                {errors.genre && (
                  <p className="text-sm text-destructive mt-1">{errors.genre.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="tags">Tags *</Label>
                <MultiSelect
                  options={["action", "adventure", "comedy", "drama", "fantasy", "romance", "sci-fi", "thriller"]}
                  value={watchedTags}
                  onChange={(tags) => setValue("tags", tags)}
                  placeholder="Select or add tags"
                  className={errors.tags ? "border-destructive" : ""}
                />
                {errors.tags && (
                  <p className="text-sm text-destructive mt-1">{errors.tags.message}</p>
                )}
              </div>

              <div className="md:col-span-2">
                <Label htmlFor="poster">Poster Image</Label>
                <div className="mt-2">
                  <input
                    id="poster"
                    type="file"
                    accept="image/*"
                    onChange={handlePosterUpload}
                    className="hidden"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => document.getElementById("poster")?.click()}
                    className="w-full"
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    Upload Poster Image
                  </Button>
                  {posterPreview && (
                    <div className="mt-4 relative inline-block">
                      <img
                        src={posterPreview}
                        alt="Poster preview"
                        className="h-32 w-24 object-cover rounded-md"
                      />
                      <Button
                        type="button"
                        variant="destructive"
                        size="sm"
                        className="absolute -top-2 -right-2 h-6 w-6 p-0"
                        onClick={() => {
                          setPosterPreview(null)
                          setValue("posterFile", undefined)
                        }}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </div>

            <div className="flex justify-end mt-6">
              <Button type="button" onClick={nextStep}>
                Next Step
              </Button>
            </div>
          </Card>
        )}

        {/* Step 2: Episodes */}
        {currentStep === 2 && (
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">Episodes ({fields.length})</h2>

            <div className="space-y-4 max-h-96 overflow-y-auto">
              {fields.map((field, index) => (
                <Card key={field.id} className="p-4 border-2">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="font-medium">Episode {index + 1}</h3>
                    {fields.length > 1 && (
                      <Button
                        type="button"
                        variant="destructive"
                        size="sm"
                        onClick={() => remove(index)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor={`episodes.${index}.title`}>Episode Title *</Label>
                      <Input
                        {...register(`episodes.${index}.title`)}
                        placeholder="Enter episode title"
                        className={errors.episodes?.[index]?.title ? "border-destructive" : ""}
                      />
                      {errors.episodes?.[index]?.title && (
                        <p className="text-sm text-destructive mt-1">
                          {errors.episodes[index]?.title?.message}
                        </p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor={`episodes.${index}.streamingUrl`}>Streaming URL *</Label>
                      <Input
                        {...register(`episodes.${index}.streamingUrl`)}
                        placeholder="https://example.com/watch/episode"
                        className={errors.episodes?.[index]?.streamingUrl ? "border-destructive" : ""}
                      />
                      {errors.episodes?.[index]?.streamingUrl && (
                        <p className="text-sm text-destructive mt-1">
                          {errors.episodes[index]?.streamingUrl?.message}
                        </p>
                      )}
                    </div>

                    <div className="md:col-span-2">
                      <Label htmlFor={`episodes.${index}.thumbnail`}>Episode Thumbnail</Label>
                      <div className="mt-2">
                        <input
                          id={`episodes.${index}.thumbnail`}
                          type="file"
                          accept="image/*"
                          onChange={(e) => handleEpisodeThumbnailUpload(index, e)}
                          className="hidden"
                        />
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => document.getElementById(`episodes.${index}.thumbnail`)?.click()}
                        >
                          <Upload className="h-4 w-4 mr-2" />
                          Upload Thumbnail
                        </Button>
                        {episodeThumbnails[index] && (
                          <div className="mt-2 relative inline-block">
                            <img
                              src={episodeThumbnails[index]}
                              alt={`Episode ${index + 1} thumbnail`}
                              className="h-16 w-24 object-cover rounded-md"
                            />
                            <Button
                              type="button"
                              variant="destructive"
                              size="sm"
                              className="absolute -top-2 -right-2 h-5 w-5 p-0"
                              onClick={() => {
                                setEpisodeThumbnails(prev => {
                                  const updated = { ...prev }
                                  delete updated[index]
                                  return updated
                                })
                                setValue(`episodes.${index}.thumbnailFile`, undefined)
                              }}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>

            {errors.episodes && (
              <p className="text-sm text-destructive mt-2">{errors.episodes.message}</p>
            )}
          </Card>
        )}

        {/* Step 3: Review & Submit */}
        {currentStep === 3 && (
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">Review & Submit</h2>

            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="font-medium text-sm text-muted-foreground">Title</h3>
                  <p>{watch("title")}</p>
                </div>
                <div>
                  <h3 className="font-medium text-sm text-muted-foreground">Season</h3>
                  <p>{watch("season")}</p>
                </div>
                <div>
                  <h3 className="font-medium text-sm text-muted-foreground">Episode Count</h3>
                  <p>{watch("episodeCount")}</p>
                </div>
                <div>
                  <h3 className="font-medium text-sm text-muted-foreground">Genre</h3>
                  <p>{watch("genre")}</p>
                </div>
                <div className="md:col-span-2">
                  <h3 className="font-medium text-sm text-muted-foreground">Tags</h3>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {watch("tags").map((tag) => (
                      <span
                        key={tag}
                        className="px-2 py-1 bg-secondary text-secondary-foreground rounded-md text-xs"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              </div>

              <div>
                <h3 className="font-medium text-sm text-muted-foreground mb-2">Episodes</h3>
                <div className="space-y-2 max-h-48 overflow-y-auto">
                  {watch("episodes").map((episode, index) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-muted rounded-md">
                      <span className="text-sm">
                        Episode {episode.episodeNumber}: {episode.title}
                      </span>
                      <span className="text-xs text-muted-foreground">
                        {episode.streamingUrl ? "✓ URL" : "✗ No URL"}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </Card>
        )}

        {/* Navigation buttons */}
        {currentStep > 1 && (
          <div className="flex justify-between">
            <Button type="button" variant="outline" onClick={prevStep}>
              Previous
            </Button>
            {currentStep === 3 ? (
              <Button type="submit" disabled={isLoading}>
                {isLoading && <LoadingSpinner size="sm" className="mr-2" />}
                {isLoading ? "Creating..." : "Create Anime"}
              </Button>
            ) : (
              <Button type="button" onClick={nextStep}>
                Next Step
              </Button>
            )}
          </div>
        )}
      </form>
    </div>
  )
}

export { AddAnimeForm }
