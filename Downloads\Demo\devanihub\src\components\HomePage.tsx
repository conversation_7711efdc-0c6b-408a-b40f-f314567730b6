import React, { useState, useMemo } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useNavigate } from 'react-router-dom'
import { Search, Plus, Filter, Grid, List, X, ChevronDown, SortAsc } from 'lucide-react'
import { useGetAnimesQuery, useGetTagsQuery } from '../store/api'
import { updateSearchFilters } from '../store/slices/uiSlice'
import type { RootState } from '../store'
import { Button } from './ui/Button'
import { Input } from './ui/Input'
import { Select } from './ui/Select'
import { Badge } from './ui/Badge'
import { LoadingSpinner } from './ui/LoadingSpinner'
import AnimeCard from './anime/AnimeCard'
import { MobileNavigation } from './layout/MobileNavigation'
import { debounce, cn } from '../lib/utils'
import { ANIME_GENRES } from '../types'

const HomePage: React.FC = () => {
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const { searchFilters } = useSelector((state: RootState) => state.ui)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [searchQuery, setSearchQuery] = useState('')
  const [showFilters, setShowFilters] = useState(false)
  const [selectedGenre, setSelectedGenre] = useState('')
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [sortBy, setSortBy] = useState<'recent' | 'alphabetical' | 'popular'>('recent')

  // Debounced search
  const debouncedSearch = useMemo(
    () => debounce((query: string) => {
      dispatch(updateSearchFilters({
        query,
        genre: selectedGenre || undefined,
        tags: selectedTags.length > 0 ? selectedTags : undefined,
        sortBy
      }))
    }, 300),
    [dispatch, selectedGenre, selectedTags, sortBy]
  )

  React.useEffect(() => {
    debouncedSearch(searchQuery)
  }, [searchQuery, selectedGenre, selectedTags, sortBy, debouncedSearch])

  const { data: animesData, isLoading, error } = useGetAnimesQuery({
    page: 1,
    limit: 12,
    filters: searchFilters,
  })

  const { data: tags } = useGetTagsQuery()

  const handleAddAnime = () => {
    navigate('/add-anime')
  }

  const handleAnimeClick = (animeId: string) => {
    navigate(`/anime/${animeId}`)
  }

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value)
  }

  const handleTagToggle = (tag: string) => {
    const newTags = selectedTags.includes(tag)
      ? selectedTags.filter(t => t !== tag)
      : [...selectedTags, tag]
    setSelectedTags(newTags)
  }

  const clearFilters = () => {
    setSearchQuery('')
    setSelectedTags([])
    setSelectedGenre('')
    setSortBy('recent')
  }

  const hasActiveFilters = searchQuery || selectedTags.length > 0 || selectedGenre

  const genreOptions = ANIME_GENRES.map(genre => ({ value: genre, label: genre }))
  const sortOptions = [
    { value: 'recent', label: 'Recently Added' },
    { value: 'alphabetical', label: 'Alphabetical' },
    { value: 'popular', label: 'Most Popular' },
  ]

  return (
    <div className="min-h-screen bg-background pb-20 md:pb-0">
      {/* Header */}
      <header className="sticky top-0 z-40 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-bold text-primary">DevAniHub</h1>
              <p className="text-sm text-muted-foreground hidden sm:block">
                Manage your anime streaming links
              </p>
            </div>
            
            <Button onClick={handleAddAnime} className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              <span className="hidden sm:inline">Add Anime</span>
            </Button>
          </div>
        </div>
      </header>

      {/* Search and Filters */}
      <div className="container mx-auto px-4 py-6">
        <div className="flex flex-col gap-4 mb-6">
          {/* Search Bar and Controls */}
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search Bar */}
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search anime by title or tags..."
                value={searchQuery}
                onChange={handleSearchChange}
                className="pl-10 min-h-[44px] touch-manipulation"
              />
            </div>

            {/* Controls */}
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
                className={cn(
                  "min-h-[44px] touch-manipulation", // Touch-friendly height
                  hasActiveFilters && "border-primary"
                )}
              >
                <Filter className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Filters</span>
                <ChevronDown className={cn("h-4 w-4 ml-2 transition-transform", showFilters && "rotate-180")} />
              </Button>

              <div className="flex items-center gap-1 border rounded-md">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                  className="rounded-r-none min-h-[44px] min-w-[44px] touch-manipulation"
                >
                  <Grid className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className="rounded-l-none min-h-[44px] min-w-[44px] touch-manipulation"
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Expanded Filters */}
          {showFilters && (
            <div className="bg-muted/50 rounded-lg p-4 space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Genre Filter */}
                <div>
                  <label className="text-sm font-medium mb-2 block">Genre</label>
                  <Select
                    options={[{ value: '', label: 'All Genres' }, ...genreOptions]}
                    value={selectedGenre}
                    onValueChange={setSelectedGenre}
                    placeholder="Select genre"
                  />
                </div>

                {/* Sort By */}
                <div>
                  <label className="text-sm font-medium mb-2 block">Sort By</label>
                  <Select
                    options={sortOptions}
                    value={sortBy}
                    onValueChange={(value) => setSortBy(value as any)}
                    placeholder="Sort by"
                  />
                </div>

                {/* Clear Filters */}
                <div className="flex items-end">
                  {hasActiveFilters && (
                    <Button variant="outline" onClick={clearFilters} className="w-full">
                      <X className="h-4 w-4 mr-2" />
                      Clear Filters
                    </Button>
                  )}
                </div>
              </div>

              {/* Tag Filters */}
              {tags && tags.length > 0 && (
                <div>
                  <label className="text-sm font-medium mb-2 block">Tags</label>
                  <div className="flex flex-wrap gap-2">
                    {tags.slice(0, 20).map((tag) => (
                      <Badge
                        key={tag.id}
                        variant={selectedTags.includes(tag.name) ? "default" : "outline"}
                        className="cursor-pointer hover:bg-primary/80"
                        onClick={() => handleTagToggle(tag.name)}
                      >
                        {tag.name} ({tag.usageCount})
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Active Filters Display */}
          {hasActiveFilters && (
            <div className="flex flex-wrap items-center gap-2">
              <span className="text-sm text-muted-foreground">Active filters:</span>
              {selectedGenre && (
                <Badge variant="secondary" className="gap-1">
                  Genre: {selectedGenre}
                  <X
                    className="h-3 w-3 cursor-pointer hover:text-destructive"
                    onClick={() => setSelectedGenre('')}
                  />
                </Badge>
              )}
              {selectedTags.map((tag) => (
                <Badge key={tag} variant="secondary" className="gap-1">
                  {tag}
                  <X
                    className="h-3 w-3 cursor-pointer hover:text-destructive"
                    onClick={() => handleTagToggle(tag)}
                  />
                </Badge>
              ))}
            </div>
          )}
        </div>

        {/* Content */}
        <main>
          {isLoading && (
            <div className="flex justify-center items-center py-12">
              <div className="text-center">
                <LoadingSpinner size="lg" className="mx-auto mb-4" />
                <p className="text-muted-foreground">Loading anime...</p>
              </div>
            </div>
          )}

          {error && (
            <div className="text-center py-12">
              <p className="text-destructive">Failed to load anime. Please try again.</p>
            </div>
          )}

          {animesData && animesData.data.length === 0 && (
            <div className="text-center py-12">
              <p className="text-muted-foreground mb-4">No anime found.</p>
              <Button onClick={handleAddAnime}>
                <Plus className="h-4 w-4 mr-2" />
                Add your first anime
              </Button>
            </div>
          )}

          {animesData && animesData.data.length > 0 && (
            <div className={
              viewMode === 'grid'
                ? "grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-3 sm:gap-4 md:gap-6"
                : "space-y-4"
            }>
              {animesData.data.map((anime) => (
                <AnimeCard
                  key={anime.id}
                  anime={anime}
                  viewMode={viewMode}
                  onClick={() => handleAnimeClick(anime.id)}
                />
              ))}
            </div>
          )}
        </main>
      </div>

      {/* Mobile Navigation */}
      <MobileNavigation />
    </div>
  )
}

export default HomePage
