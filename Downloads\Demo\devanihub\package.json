{"name": "<PERSON><PERSON><PERSON><PERSON>", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@reduxjs/toolkit": "^2.8.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/react-router-dom": "^5.3.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "firebase": "^11.9.1", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.59.0", "react-hot-toast": "^2.5.2", "react-redux": "^9.2.0", "react-router-dom": "^7.6.3", "tailwind-merge": "^3.3.1", "zod": "^3.25.67"}, "devDependencies": {"@eslint/js": "^9.29.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "jsdom": "^26.1.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "vite": "^5.4.19", "vitest": "^3.2.4"}}